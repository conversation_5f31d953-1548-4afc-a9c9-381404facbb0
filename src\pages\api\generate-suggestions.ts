import type { APIRoute } from 'astro';
import { supabase } from '../../lib/supabase';
import { analyzeLandingPage } from '../../lib/ai';
import type { AnalysisResult } from '../../types/analysis';

interface DetailedSuggestion {
  category: string;
  title: string;
  description: string;
  detailed_explanation: string;
  impact_level: 'High' | 'Medium' | 'Low';
  effort_level: 'High' | 'Medium' | 'Low';
  priority: number;
  implementation_steps: string;
  expected_impact: string;
  business_value: string;
}

async function generateDetailedSuggestions(
  recommendations: string[],
  content: string,
  url: string,
  conversionScore: number
): Promise<DetailedSuggestion[]> {
  const suggestions: DetailedSuggestion[] = [];

  // Analyze conversion score to determine priority areas
  const needsCriticalWork = conversionScore < 4;
  const needsModerateWork = conversionScore < 6;

  for (let i = 0; i < recommendations.length && i < 4; i++) {
    const recommendation = recommendations[i];
    let suggestion: DetailedSuggestion;

    // Generate contextual suggestions based on the recommendation content
    if (recommendation.toLowerCase().includes('call') || recommendation.toLowerCase().includes('cta') || recommendation.toLowerCase().includes('button')) {
      suggestion = {
        category: 'Conversion',
        title: 'Optimize Call-to-Action Elements',
        description: 'Improve CTA button placement, design, and messaging to increase conversion rates.',
        detailed_explanation: `Your current call-to-action elements need optimization to maximize conversions. Based on the analysis, improving CTA visibility, positioning, and messaging can significantly impact your conversion rate. Consider using action-oriented language, contrasting colors, and strategic placement above the fold.`,
        impact_level: needsCriticalWork ? 'High' : 'Medium',
        effort_level: 'Medium',
        priority: i + 1,
        implementation_steps: '1. Audit current CTA buttons and their placement\n2. Move primary CTA above the fold\n3. Use contrasting colors (orange, red, or green)\n4. Increase button size and add padding\n5. Use action-oriented text like "Get Started Now" or "Claim Your Free Trial"\n6. A/B test different variations',
        expected_impact: needsCriticalWork ? 'Expected 15-25% increase in conversion rate' : 'Expected 8-15% increase in conversion rate',
        business_value: 'Higher conversion rates directly translate to more leads, sales, and revenue growth'
      };
    } else if (recommendation.toLowerCase().includes('trust') || recommendation.toLowerCase().includes('social') || recommendation.toLowerCase().includes('proof')) {
      suggestion = {
        category: 'Trust',
        title: 'Add Social Proof and Trust Signals',
        description: 'Include customer testimonials, reviews, and trust badges to build credibility and reduce visitor hesitation.',
        detailed_explanation: `Social proof is crucial for building trust with potential customers. Adding testimonials, customer logos, review scores, and security badges helps visitors feel confident about choosing your service. This is especially important for new visitors who don't know your brand.`,
        impact_level: 'High',
        effort_level: 'Low',
        priority: i + 1,
        implementation_steps: '1. Collect customer testimonials and case studies\n2. Add review widgets or display review scores\n3. Include customer company logos\n4. Add security badges and certifications\n5. Include "As seen in" media mentions\n6. Display user count or social media followers',
        expected_impact: 'Expected 10-20% increase in conversion rate',
        business_value: 'Trust signals reduce friction in the buying process and increase customer confidence'
      };
    } else if (recommendation.toLowerCase().includes('content') || recommendation.toLowerCase().includes('value') || recommendation.toLowerCase().includes('proposition')) {
      suggestion = {
        category: 'Content',
        title: 'Improve Value Proposition Clarity',
        description: 'Clarify your unique value proposition to better communicate benefits and differentiate from competitors.',
        detailed_explanation: `A clear, compelling value proposition is essential for conversions. Visitors should immediately understand what you offer, how it benefits them, and why they should choose you over competitors. Your current messaging may be unclear or buried in too much text.`,
        impact_level: needsModerateWork ? 'High' : 'Medium',
        effort_level: 'Medium',
        priority: i + 1,
        implementation_steps: '1. Audit current messaging and headlines\n2. Identify your unique benefits and differentiators\n3. Create a clear, compelling headline\n4. Add supporting subheadlines that explain benefits\n5. Use bullet points for key value propositions\n6. Remove jargon and use simple language',
        expected_impact: 'Expected 8-15% increase in conversion rate',
        business_value: 'Clear messaging reduces confusion and attracts more qualified leads'
      };
    } else {
      // Generic improvement suggestion
      suggestion = {
        category: 'Optimization',
        title: 'General Landing Page Enhancement',
        description: 'Implement comprehensive improvements to boost overall page performance and user experience.',
        detailed_explanation: `Based on the analysis of your landing page, there are several areas for improvement that can enhance user experience and increase conversions. These optimizations focus on making your page more engaging and persuasive.`,
        impact_level: 'Medium',
        effort_level: 'Medium',
        priority: i + 1,
        implementation_steps: '1. Analyze current page performance\n2. Identify key improvement areas\n3. Implement changes systematically\n4. Test and measure results\n5. Iterate based on data',
        expected_impact: 'Expected 5-12% increase in conversion rate',
        business_value: 'Comprehensive optimizations improve overall user experience and business results'
      };
    }

    suggestions.push(suggestion);
  }

  return suggestions;
}

export const POST: APIRoute = async ({ request }) => {
  try {
    const { url, analysisId } = await request.json();

    if (!url || !analysisId) {
      return new Response(
        JSON.stringify({ error: 'URL and analysisId are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Fetch website content
    const scrapeResponse = await fetch(new URL('/api/scrape-website', request.url).toString(), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url })
    });

    if (!scrapeResponse.ok) {
        const errorText = await scrapeResponse.text();
        console.error('Failed to scrape website:', errorText);
        return new Response(JSON.stringify({ error: 'Failed to scrape website' }), { status: 500 });
    }

    const { content } = await scrapeResponse.json();

    // Get AI-powered analysis
    const analysis: AnalysisResult = await analyzeLandingPage(content, url);

    // Generate high-quality suggestions with detailed content
    const suggestions = await generateDetailedSuggestions(analysis.recommendations, content, url, analysis.score);

    // Add analysis_id to each suggestion
    const suggestionsWithAnalysisId = suggestions.map(suggestion => ({
        ...suggestion,
        analysis_id: analysisId
    }));

    const { error: dbError } = await supabase.from('suggestions').insert(suggestionsWithAnalysisId);

    if (dbError) {
        console.error('Error saving suggestions:', dbError);
        return new Response(JSON.stringify({ error: 'Failed to save suggestions' }), { status: 500 });
    }

    // Update the analysis with the new data
    await supabase.from('analyses').update({
        pros: JSON.stringify(analysis.pros),
        cons: JSON.stringify(analysis.cons),
        target_audience: analysis.targetAudience,
        conversion_rate: analysis.conversionRate,
        score: analysis.score,
    }).eq('id', analysisId);


    return new Response(JSON.stringify({ success: true }), { status: 200 });

  } catch (error) {
    console.error('Error generating suggestions:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), { status: 500 });
  }
};