@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  body {
    @apply antialiased text-gray-900 bg-background-primary;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* Clean scrollbar styling matching reference design */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

@layer components {
  /* Button styles matching reference design */
  .btn-primary {
    @apply inline-flex items-center justify-center px-4 py-2.5 bg-primary-500 hover:bg-primary-600 focus:bg-primary-600 text-white text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-4 py-2.5 bg-white hover:bg-gray-50 focus:bg-gray-50 text-gray-700 text-sm font-medium rounded-lg border border-gray-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md;
  }

  .btn-ghost {
    @apply inline-flex items-center justify-center px-4 py-2.5 bg-transparent hover:bg-gray-50 focus:bg-gray-50 text-gray-700 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* Card styles matching reference design */
  .card {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm;
  }

  .card-hover {
    @apply hover:shadow-md transition-all duration-200 hover:border-gray-300;
  }

  .card-interactive {
    @apply block bg-white rounded-xl cursor-pointer transition-all duration-200 hover:shadow-md hover:bg-gray-50;
  }

  /* Form input styles matching reference design */
  .input-field {
    @apply block w-full px-3 py-2.5 text-gray-900 bg-white border border-gray-300 rounded-lg text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200;
  }

  .input-field-lg {
    @apply block w-full px-4 py-3 text-base text-gray-900 bg-white border border-gray-300 rounded-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200;
  }

  /* Navigation styles */
  .nav-link {
    @apply text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors duration-200;
  }

  .nav-link-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-gray-600 hover:text-gray-900 hover:bg-gray-50;
  }

  .nav-link-active {
    @apply bg-gray-100 text-gray-900 shadow-sm;
  }

  /* Status badges matching reference design */
  .badge-success {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-50 text-success-600;
  }

  .badge-warning {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-50 text-warning-600;
  }

  .badge-error {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-error-50 text-error-600;
  }

  .badge-neutral {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600;
  }

  /* Metric card styles */
  .metric-card {
    @apply bg-white rounded-xl border border-gray-200 p-6 shadow-sm hover:shadow-md transition-all duration-200;
  }

  .metric-value {
    @apply text-3xl font-bold text-gray-900;
  }

  .metric-label {
    @apply text-sm font-medium text-gray-600;
  }

  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  /* Simple fade-in animation for content */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  /* Ensure content is always visible */
  .content-visible {
    opacity: 1 !important;
    transform: none !important;
  }
}