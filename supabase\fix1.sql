-- ConvertIQ Database Fixes
-- This file addresses critical database issues identified in the application

-- 1. Ensure screenshots storage bucket exists and has proper policies
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES ('screenshots', 'screenshots', true, 52428800, ARRAY['image/png', 'image/jpeg', 'image/webp'])
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800,
  allowed_mime_types = ARRAY['image/png', 'image/jpeg', 'image/webp'];

-- 2. Create storage policies for screenshots bucket
DROP POLICY IF EXISTS "Public Access" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload screenshots" ON storage.objects;
DROP POLICY IF EXISTS "Public can view screenshots" ON storage.objects;

CREATE POLICY "Authenticated users can upload screenshots" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'screenshots' AND auth.role() = 'authenticated');

CREATE POLICY "Public can view screenshots" ON storage.objects
  FOR SELECT USING (bucket_id = 'screenshots');

CREATE POLICY "Authenticated users can update screenshots" ON storage.objects
  FOR UPDATE USING (bucket_id = 'screenshots' AND auth.role() = 'authenticated');

-- 3. Fix performance_metrics table to ensure it gets populated
-- Add trigger to automatically populate performance_metrics when lighthouse_data is updated
CREATE OR REPLACE FUNCTION populate_performance_metrics()
RETURNS TRIGGER AS $$
BEGIN
  -- Delete existing metrics for this analysis
  DELETE FROM performance_metrics WHERE analysis_id = NEW.id;
  
  -- Insert Core Web Vitals metrics if lighthouse_data exists
  IF NEW.lighthouse_data IS NOT NULL THEN
    -- LCP (Largest Contentful Paint)
    IF NEW.lcp_score IS NOT NULL THEN
      INSERT INTO performance_metrics (analysis_id, metric_name, metric_value, metric_unit, metric_category, is_good, threshold_good, threshold_poor)
      VALUES (NEW.id, 'Largest Contentful Paint', NEW.lcp_score, 'seconds', 'Core Web Vitals', NEW.lcp_score <= 2.5, 2.5, 4.0);
    END IF;
    
    -- FID (First Input Delay)
    IF NEW.fid_score IS NOT NULL THEN
      INSERT INTO performance_metrics (analysis_id, metric_name, metric_value, metric_unit, metric_category, is_good, threshold_good, threshold_poor)
      VALUES (NEW.id, 'First Input Delay', NEW.fid_score, 'milliseconds', 'Core Web Vitals', NEW.fid_score <= 100, 100, 300);
    END IF;
    
    -- CLS (Cumulative Layout Shift)
    IF NEW.cls_score IS NOT NULL THEN
      INSERT INTO performance_metrics (analysis_id, metric_name, metric_value, metric_unit, metric_category, is_good, threshold_good, threshold_poor)
      VALUES (NEW.id, 'Cumulative Layout Shift', NEW.cls_score, '', 'Core Web Vitals', NEW.cls_score <= 0.1, 0.1, 0.25);
    END IF;
    
    -- Performance Score
    IF NEW.performance_score IS NOT NULL THEN
      INSERT INTO performance_metrics (analysis_id, metric_name, metric_value, metric_unit, metric_category, is_good, threshold_good, threshold_poor)
      VALUES (NEW.id, 'Performance Score', NEW.performance_score, 'score', 'Overall', NEW.performance_score >= 90, 90, 50);
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for performance metrics population
DROP TRIGGER IF EXISTS trigger_populate_performance_metrics ON analyses;
CREATE TRIGGER trigger_populate_performance_metrics
  AFTER INSERT OR UPDATE OF lighthouse_data, lcp_score, fid_score, cls_score, performance_score
  ON analyses
  FOR EACH ROW
  EXECUTE FUNCTION populate_performance_metrics();

-- 4. Improve suggestions table structure and add better content generation
-- Add columns for better suggestion quality
ALTER TABLE suggestions 
ADD COLUMN IF NOT EXISTS implementation_steps TEXT,
ADD COLUMN IF NOT EXISTS expected_impact TEXT,
ADD COLUMN IF NOT EXISTS business_value TEXT;

-- 5. Improve SEO issues table to prevent duplicates and ensure unique content
-- Add unique constraint to prevent duplicate SEO issues for same analysis
ALTER TABLE seo_issues 
ADD COLUMN IF NOT EXISTS issue_type TEXT DEFAULT 'general',
ADD COLUMN IF NOT EXISTS fix_instructions TEXT,
ADD COLUMN IF NOT EXISTS impact_description TEXT;

-- Create unique constraint to prevent duplicate issues per analysis
DROP INDEX IF EXISTS idx_seo_issues_unique;
CREATE UNIQUE INDEX idx_seo_issues_unique ON seo_issues (analysis_id, issue_type, issue);

-- 6. Add function to generate better suggestions
CREATE OR REPLACE FUNCTION generate_quality_suggestions(analysis_id_param UUID, website_content TEXT, conversion_score NUMERIC)
RETURNS VOID AS $$
DECLARE
  suggestion_record RECORD;
BEGIN
  -- Delete existing suggestions for this analysis
  DELETE FROM suggestions WHERE analysis_id = analysis_id_param;
  
  -- Generate suggestions based on conversion score
  IF conversion_score < 4 THEN
    -- Critical improvements needed
    INSERT INTO suggestions (analysis_id, category, title, description, detailed_explanation, impact_level, effort_level, priority, implementation_steps, expected_impact, business_value)
    VALUES 
    (analysis_id_param, 'Conversion', 'Optimize Call-to-Action Placement', 
     'Primary CTA buttons need better positioning and visibility to increase conversion rates.',
     'Current call-to-action elements may be poorly positioned or lack visual prominence. Moving CTAs above the fold and using contrasting colors can significantly improve click-through rates.',
     'High', 'Medium', 1,
     '1. Identify primary CTA buttons\n2. Move main CTA above the fold\n3. Use contrasting colors (orange/red)\n4. Increase button size and padding\n5. Add urgency text like "Get Started Now"',
     'Expected 15-25% increase in conversion rate',
     'Higher conversion rates directly translate to more leads and revenue');
     
    INSERT INTO suggestions (analysis_id, category, title, description, detailed_explanation, impact_level, effort_level, priority, implementation_steps, expected_impact, business_value)
    VALUES 
    (analysis_id_param, 'Trust', 'Add Social Proof Elements', 
     'Include customer testimonials, reviews, and trust badges to build credibility.',
     'Social proof is crucial for conversion optimization. Adding testimonials, customer logos, review scores, and security badges helps visitors trust your brand and feel confident about converting.',
     'High', 'Low', 2,
     '1. Collect customer testimonials\n2. Add review widgets or scores\n3. Display customer logos\n4. Include security badges\n5. Add "As seen in" media mentions',
     'Expected 10-20% increase in conversion rate',
     'Trust signals reduce friction and increase customer confidence');
  END IF;
  
  IF conversion_score < 6 THEN
    INSERT INTO suggestions (analysis_id, category, title, description, detailed_explanation, impact_level, effort_level, priority, implementation_steps, expected_impact, business_value)
    VALUES 
    (analysis_id_param, 'Content', 'Improve Value Proposition Clarity', 
     'Clarify your unique value proposition to better communicate benefits to visitors.',
     'A clear, compelling value proposition is essential for conversions. Visitors should immediately understand what you offer, how it benefits them, and why they should choose you over competitors.',
     'Medium', 'Medium', 3,
     '1. Audit current messaging\n2. Identify unique benefits\n3. Create clear headline\n4. Add supporting subheadlines\n5. Use bullet points for key benefits',
     'Expected 8-15% increase in conversion rate',
     'Clear messaging reduces confusion and increases qualified leads');
  END IF;
  
  -- Always add a performance suggestion if performance score is low
  INSERT INTO suggestions (analysis_id, category, title, description, detailed_explanation, impact_level, effort_level, priority, implementation_steps, expected_impact, business_value)
  VALUES 
  (analysis_id_param, 'Performance', 'Optimize Page Loading Speed', 
   'Improve website performance to reduce bounce rate and increase conversions.',
   'Page speed directly impacts conversion rates. Studies show that even 1-second delays can decrease conversions by 7%. Optimizing images, enabling compression, and using CDNs can significantly improve performance.',
   'High', 'High', 4,
   '1. Compress and optimize images\n2. Enable GZIP compression\n3. Minify CSS and JavaScript\n4. Use a Content Delivery Network\n5. Optimize server response time',
   'Expected 5-15% increase in conversion rate',
   'Faster pages improve user experience and search rankings');
END;
$$ LANGUAGE plpgsql;

-- 7. Add function to generate unique SEO issues
CREATE OR REPLACE FUNCTION generate_unique_seo_issues(analysis_id_param UUID, website_url TEXT, seo_score INTEGER)
RETURNS VOID AS $$
BEGIN
  -- Delete existing SEO issues for this analysis
  DELETE FROM seo_issues WHERE analysis_id = analysis_id_param;
  
  -- Generate SEO issues based on score and website characteristics
  IF seo_score < 70 THEN
    INSERT INTO seo_issues (analysis_id, issue, recommendation, severity_score, issue_type, fix_instructions, impact_description)
    VALUES 
    (analysis_id_param, 
     'Missing or inadequate meta description', 
     'Add compelling meta descriptions to improve click-through rates from search results',
     4, 'meta_tags',
     'Add a meta description tag with 150-160 characters that includes your target keywords and compelling copy that encourages clicks',
     'Meta descriptions can improve CTR by 5-15% and help with search engine rankings');
     
    INSERT INTO seo_issues (analysis_id, issue, recommendation, severity_score, issue_type, fix_instructions, impact_description)
    VALUES 
    (analysis_id_param, 
     'Suboptimal heading structure', 
     'Improve heading hierarchy (H1-H6) for better content organization and SEO',
     3, 'content_structure',
     '1. Ensure only one H1 tag per page\n2. Use H2-H6 tags in logical order\n3. Include target keywords in headings\n4. Make headings descriptive and user-friendly',
     'Proper heading structure improves accessibility and helps search engines understand content hierarchy');
  END IF;
  
  IF seo_score < 80 THEN
    INSERT INTO seo_issues (analysis_id, issue, recommendation, severity_score, issue_type, fix_instructions, impact_description)
    VALUES 
    (analysis_id_param, 
     'Images missing alt text', 
     'Add descriptive alt text to all images for accessibility and SEO benefits',
     2, 'accessibility',
     'Add alt attributes to all images with descriptive text that explains the image content and includes relevant keywords when appropriate',
     'Alt text improves accessibility for screen readers and provides SEO value for image search');
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 8. Update existing analyses to populate missing data
-- This will trigger the functions to populate performance_metrics and improve suggestions
UPDATE analyses SET updated_at = NOW() WHERE id IN (
  SELECT id FROM analyses WHERE 
  (SELECT COUNT(*) FROM performance_metrics WHERE analysis_id = analyses.id) = 0
  OR (SELECT COUNT(*) FROM suggestions WHERE analysis_id = analyses.id AND implementation_steps IS NOT NULL) = 0
);

-- 9. Grant necessary permissions
GRANT EXECUTE ON FUNCTION populate_performance_metrics() TO authenticated;
GRANT EXECUTE ON FUNCTION generate_quality_suggestions(UUID, TEXT, NUMERIC) TO authenticated;
GRANT EXECUTE ON FUNCTION generate_unique_seo_issues(UUID, TEXT, INTEGER) TO authenticated;

-- 10. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_performance_metrics_analysis_id ON performance_metrics(analysis_id);
CREATE INDEX IF NOT EXISTS idx_suggestions_analysis_id ON suggestions(analysis_id);
CREATE INDEX IF NOT EXISTS idx_seo_issues_analysis_id ON seo_issues(analysis_id);
CREATE INDEX IF NOT EXISTS idx_analyses_screenshot_url ON analyses(screenshot_url) WHERE screenshot_url IS NOT NULL;

-- End of fix1.sql
