<template>
  <div
    class="space-y-8"
  >
    <!-- Stats Cards Skeleton -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div
        v-for="i in 4" :key="i" class="metric-card"
      >
        <div class="animate-pulse flex items-center">
          <div class="w-12 h-12 bg-gray-200 rounded-xl"></div>
          <div class="ml-4 flex-1">
            <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-6 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Analyses Skeleton -->
    <div
      class="card"
    >
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="animate-pulse flex items-center justify-between">
          <div class="h-6 bg-gray-200 rounded w-1/3"></div>
          <div class="h-4 bg-gray-200 rounded w-24"></div>
        </div>
      </div>
      <div class="p-6">
        <div class="animate-pulse space-y-4">
          <div
            v-for="i in 3" :key="i" class="flex items-center space-x-4 p-4 rounded-lg border border-gray-200"
          >
            <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
            <div class="flex-1 space-y-2">
              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
              <div class="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
            <div class="h-8 bg-gray-200 rounded w-20"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMotion } from '@vueuse/motion';
</script>