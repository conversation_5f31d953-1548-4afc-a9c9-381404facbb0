{"name": "convertiq", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev --host", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/check": "^0.5.9", "@astrojs/node": "^9.3.3", "@astrojs/tailwind": "^5.1.0", "@astrojs/vue": "^5.1.0", "@headlessui/vue": "^1.7.23", "@supabase/supabase-js": "^2.39.7", "@types/dompurify": "^3.0.5", "@types/jsdom": "^21.1.7", "@types/node": "^20.11.28", "@vueuse/motion": "^3.0.3", "astro": "^5.12.8", "chrome-launcher": "^1.1.2", "dompurify": "^3.2.6", "jsdom": "^26.1.0", "lighthouse": "^11.7.1", "lucide-react": "^0.525.0", "lucide-vue-next": "^0.525.0", "markdown-it": "^14.1.0", "openai": "^4.28.4", "puppeteer": "^22.15.0", "tailwindcss": "^3.4.1", "typescript": "^5.4.2", "vue": "^3.4.21", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "autoprefixer": "^10.4.17", "postcss": "^8.4.35"}}