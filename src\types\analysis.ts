export interface AnalysisResult {
  score: number;
  conversionRate: number;
  pros: string[];
  cons: string[];
  recommendations: string[];
  targetAudience: string;
  adaptations: string[];
}

export interface AnalysisWithMetadata extends AnalysisResult {
  id: string;
  user_id: string;
  url: string;
  title: string;
  created_at: string;
  updated_at: string;
  analysis_data?: {
    seoScore: number;
    contentQuality: number;
    userExperience: number;
    technicalScore: number;
    summary: string;
    scrapedData: any;
  };
}

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  created_at: string;
}

export interface EnhancedAnalysisResult extends AnalysisResult {
  performanceInsights: {
    score: number;
    grade: 'A' | 'B' | 'C' | 'D' | 'F';
    conversionImpact: string;
    recommendations: Array<{
      title: string;
      description: string;
      impact: 'High' | 'Medium' | 'Low';
      effort: 'High' | 'Medium' | 'Low';
      category: string;
    }>;
  };
  seoInsights: {
    score: number;
    issues: Array<{
      issue: string;
      recommendation: string;
      severity_score: number;
    }>;
    pros: string[];
    cons: string[];
  };
  leadInsights: {
    targetAudience: string;
    qualificationQuestions: string[];
    businessImpact: string[];
    roadmapSuggestions: Array<{
      phase: string;
      title: string;
      description: string;
      timeline: string;
      expectedImpact: string;
    }>;
  };
  overallGrade: 'A' | 'B' | 'C' | 'D' | 'F';
}