<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useMotion } from '@vueuse/motion';
import { CheckCircle, Clock, Loader2, AlertCircle } from 'lucide-vue-next';

interface ProcessStep {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  duration?: number; // in milliseconds
}

const props = defineProps<{
  steps: ProcessStep[];
  autoStart?: boolean;
  onComplete?: () => void;
  onStepComplete?: (stepId: string) => void;
}>();

const emit = defineEmits<{
  complete: [];
  stepComplete: [stepId: string];
}>();

const currentStepIndex = ref(-1);
const isRunning = ref(false);
const isComplete = ref(false);
const animationSpeed = ref(400); // 400ms for smooth animations

const currentStep = computed(() => {
  if (currentStepIndex.value >= 0 && currentStepIndex.value < props.steps.length) {
    return props.steps[currentStepIndex.value];
  }
  return null;
});

const completedSteps = computed(() => {
  return props.steps.slice(0, currentStepIndex.value);
});

const remainingSteps = computed(() => {
  return props.steps.slice(currentStepIndex.value + 1);
});

const progressPercentage = computed(() => {
  if (props.steps.length === 0) return 0;
  return Math.round((currentStepIndex.value / props.steps.length) * 100);
});

const startProcess = async () => {
  if (isRunning.value || isComplete.value) return;
  
  isRunning.value = true;
  currentStepIndex.value = 0;

  for (let i = 0; i < props.steps.length; i++) {
    currentStepIndex.value = i;
    const step = props.steps[i];
    
    // Update step status to running
    step.status = 'running';
    
    // Wait for step duration or default 1.5 seconds
    const duration = step.duration || 1500;
    await new Promise(resolve => setTimeout(resolve, duration));
    
    // Mark step as completed
    step.status = 'completed';
    
    // Emit step completion
    emit('stepComplete', step.id);
    props.onStepComplete?.(step.id);
    
    // Small delay between steps for visual effect
    await new Promise(resolve => setTimeout(resolve, 200));
  }

  // Process completed
  currentStepIndex.value = props.steps.length;
  isRunning.value = false;
  isComplete.value = true;
  
  // Wait a bit before emitting completion
  await new Promise(resolve => setTimeout(resolve, 500));
  
  emit('complete');
  props.onComplete?.();
};

const resetProcess = () => {
  currentStepIndex.value = -1;
  isRunning.value = false;
  isComplete.value = false;
  
  // Reset all step statuses
  props.steps.forEach(step => {
    step.status = 'pending';
  });
};

const getStepIcon = (step: ProcessStep, index: number) => {
  if (step.status === 'completed') return CheckCircle;
  if (step.status === 'running') return Loader2;
  if (step.status === 'error') return AlertCircle;
  return Clock;
};

const getStepIconClass = (step: ProcessStep, index: number) => {
  const baseClass = 'w-5 h-5 transition-all duration-400';
  
  if (step.status === 'completed') return `${baseClass} text-green-600`;
  if (step.status === 'running') return `${baseClass} text-blue-600 animate-spin`;
  if (step.status === 'error') return `${baseClass} text-red-600`;
  return `${baseClass} text-gray-400`;
};

const getStepClass = (step: ProcessStep, index: number) => {
  const baseClass = 'flex items-center space-x-3 p-4 rounded-lg transition-all duration-400';
  
  if (step.status === 'completed') return `${baseClass} bg-green-50 border border-green-200`;
  if (step.status === 'running') return `${baseClass} bg-blue-50 border border-blue-200 shadow-md`;
  if (step.status === 'error') return `${baseClass} bg-red-50 border border-red-200`;
  return `${baseClass} bg-gray-50 border border-gray-200`;
};

onMounted(() => {
  if (props.autoStart) {
    startProcess();
  }
});

defineExpose({
  startProcess,
  resetProcess,
  isRunning,
  isComplete,
  currentStepIndex,
  progressPercentage
});
</script>

<template>
  <div
    class="w-full max-w-2xl mx-auto"
  >
    <!-- Progress Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-2">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isComplete ? 'Process Complete!' : isRunning ? 'Processing...' : 'Ready to Start' }}
        </h3>
        <span class="text-sm font-medium text-gray-600">
          {{ progressPercentage }}%
        </span>
      </div>
      
      <!-- Progress Bar -->
      <div class="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
        <div
          class="h-full bg-gradient-to-r from-blue-500 to-green-500 transition-all duration-500 ease-out"
          :style="{ width: `${progressPercentage}%` }"
        ></div>
      </div>
    </div>

    <!-- Steps List -->
    <div class="space-y-3">
      <div
        v-for="(step, index) in steps"
        :key="step.id"
        :class="getStepClass(step, index)"
      >
        <!-- Step Icon -->
        <div class="flex-shrink-0">
          <component
            :is="getStepIcon(step, index)"
            :class="getStepIconClass(step, index)"
          />
        </div>
        
        <!-- Step Content -->
        <div class="flex-1 min-w-0">
          <h4 class="text-sm font-medium text-gray-900">
            {{ step.title }}
          </h4>
          <p v-if="step.description" class="text-xs text-gray-600 mt-1">
            {{ step.description }}
          </p>
        </div>
        
        <!-- Step Status -->
        <div class="flex-shrink-0">
          <span
            v-if="step.status === 'completed'"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
          >
            Done
          </span>
          <span
            v-else-if="step.status === 'running'"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
          >
            Running
          </span>
          <span
            v-else-if="step.status === 'error'"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"
          >
            Error
          </span>
          <span
            v-else
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600"
          >
            Pending
          </span>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-6 flex space-x-3">
      <button
        v-if="!isRunning && !isComplete"
        @click="startProcess"
        class="btn-primary"
      >
        Start Process
      </button>
      
      <button
        v-if="isComplete"
        @click="resetProcess"
        class="btn-secondary"
      >
        Reset
      </button>
    </div>

    <!-- Completion Message -->
    <div
      v-if="isComplete"
      class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg"
    >
      <div class="flex items-center">
        <CheckCircle class="w-5 h-5 text-green-600 mr-3" />
        <div>
          <h4 class="text-sm font-medium text-green-800">
            All steps completed successfully!
          </h4>
          <p class="text-xs text-green-700 mt-1">
            The process has finished and all tasks are complete.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}

.btn-secondary {
  @apply inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}
</style>
