export const prerender = false;

import type { APIRoute } from 'astro';
import { createClient } from '@supabase/supabase-js';

// Singleton pattern for API route supabase client
let apiSupabaseClient: ReturnType<typeof createClient> | null = null;

function getSupabaseClient(accessToken: string) {
  if (!apiSupabaseClient) {
    apiSupabaseClient = createClient(
      import.meta.env.PUBLIC_SUPABASE_URL,
      import.meta.env.PUBLIC_SUPABASE_ANON_KEY,
      {
        global: { headers: { Authorization: `Bearer ${accessToken}` } },
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
  }
  return apiSupabaseClient;
}

export const POST: APIRoute = async ({ request }) => {
  console.log("API route /api/cache-avatar hit at:", new Date().toISOString());
  console.log("Request Content-Type:", request.headers.get('content-type'));

  let requestBodyText = '';
  try {
    requestBodyText = await request.text();
    console.log("Raw Request Body:", requestBodyText);
  } catch (e) {
    console.error("Error reading raw request body:", e);
  }

  try {
    const { avatarUrl, userId, accessToken } = JSON.parse(requestBodyText);

    if (!avatarUrl || !userId || !accessToken) {
      return new Response(JSON.stringify({ error: 'Missing avatarUrl, userId, or accessToken in parsed JSON' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const supabase = getSupabaseClient(accessToken);

    // 1. Fetch the image from the original URL
    const response = await fetch(avatarUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch avatar from ${avatarUrl}: ${response.statusText}`);
    }

    const imageBuffer = await response.arrayBuffer();

    // 2. Upload to Supabase Storage
    const filePath = `${userId}/avatar.png`; // Store in user's UID folder within the 'avatars' bucket
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('avatars') // Ensure you have a bucket named 'avatars' in Supabase Storage
      .upload(filePath, imageBuffer, {
        contentType: response.headers.get('content-type') || 'image/png',
        upsert: true, // Overwrite if already exists
      });

    if (uploadError) {
      throw new Error(`Supabase Storage upload failed: ${uploadError.message}`);
    }

    // 3. Get public URL
    const { data: publicUrlData } = supabase.storage
      .from('avatars')
      .getPublicUrl(filePath);

    if (!publicUrlData) {
      throw new Error('Failed to get public URL for uploaded avatar');
    }

    const newAvatarUrl = `${publicUrlData.publicUrl}?t=${new Date().getTime()}`;

    // 4. Update user's profile in the database
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ avatar_url: newAvatarUrl })
      .eq('id', userId);

    if (updateError) {
      throw new Error(`Supabase profile update failed: ${updateError.message}`);
    }

    return new Response(JSON.stringify({ newAvatarUrl }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in /api/cache-avatar catch block:', error);
    return new Response(JSON.stringify({
      error: 'Failed to cache avatar',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
