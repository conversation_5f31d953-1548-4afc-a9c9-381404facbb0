import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { log } from '../../lib/logger';

export const prerender = false;

export const GET: APIRoute = async ({ request }) => {
  try {
    const url = new URL(request.url);
    const analysisId = url.searchParams.get('analysisId');

    if (!analysisId) {
      return new Response(JSON.stringify({ error: 'Missing analysisId parameter' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Fetch analysis progress data
    const { data: analysis, error: analysisError } = await supabaseAdmin
      .from('analyses')
      .select(`
        id,
        generation_status,
        current_generation_step,
        completed_generation_steps,
        total_generation_steps,
        generation_started_at,
        generation_completed_at,
        generation_error,
        all_content_generated,
        pros_cons_generated,
        lead_insights_generated,
        performance_insights_generated,
        seo_insights_generated,
        chat_context_generated,
        page_summary_enhanced
      `)
      .eq('id', analysisId)
      .single();

    if (analysisError || !analysis) {
      log.error(`Failed to fetch analysis progress: ${analysisError?.message}`);
      return new Response(JSON.stringify({ error: 'Analysis not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Fetch detailed progress steps
    const { data: progressSteps, error: progressError } = await supabaseAdmin
      .from('analysis_generation_progress')
      .select('*')
      .eq('analysis_id', analysisId)
      .order('step_number');

    if (progressError) {
      log.error(`Failed to fetch progress steps: ${progressError.message}`);
    }

    // Calculate overall progress percentage
    const progressPercentage = analysis.total_generation_steps > 0 
      ? Math.round((analysis.completed_generation_steps / analysis.total_generation_steps) * 100)
      : 0;

    // Determine current status message
    let statusMessage = '';
    let isComplete = false;
    let hasError = false;

    switch (analysis.generation_status) {
      case 'pending':
        statusMessage = 'Analysis queued for processing...';
        break;
      case 'in_progress':
        statusMessage = analysis.current_generation_step || 'Processing analysis...';
        break;
      case 'completed':
        statusMessage = 'Analysis completed successfully!';
        isComplete = true;
        break;
      case 'failed':
        statusMessage = analysis.generation_error || 'Analysis failed. Please try again.';
        hasError = true;
        break;
      default:
        statusMessage = 'Unknown status';
    }

    // Format progress steps for frontend consumption
    const formattedSteps = progressSteps?.map(step => ({
      stepNumber: step.step_number,
      stepName: step.current_step,
      description: step.step_description,
      status: step.status,
      startedAt: step.started_at,
      completedAt: step.completed_at,
      errorMessage: step.error_message,
      isActive: step.status === 'in_progress',
      isCompleted: step.status === 'completed',
      isFailed: step.status === 'failed'
    })) || [];

    // Content generation status
    const contentStatus = {
      pageSummary: analysis.page_summary_enhanced,
      prosAndCons: analysis.pros_cons_generated,
      leadInsights: analysis.lead_insights_generated,
      performanceInsights: analysis.performance_insights_generated,
      seoInsights: analysis.seo_insights_generated,
      chatContext: analysis.chat_context_generated,
      allContent: analysis.all_content_generated
    };

    const response = {
      analysisId: analysis.id,
      status: analysis.generation_status,
      statusMessage,
      currentStep: analysis.current_generation_step,
      progressPercentage,
      completedSteps: analysis.completed_generation_steps,
      totalSteps: analysis.total_generation_steps,
      isComplete,
      hasError,
      startedAt: analysis.generation_started_at,
      completedAt: analysis.generation_completed_at,
      errorMessage: analysis.generation_error,
      steps: formattedSteps,
      contentStatus,
      lastUpdated: new Date().toISOString()
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    log.error(`Error fetching analysis progress: ${error}`);
    
    return new Response(JSON.stringify({
      error: 'Failed to fetch analysis progress',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// POST endpoint for updating progress (for internal use)
export const POST: APIRoute = async ({ request }) => {
  try {
    const { analysisId, stepNumber, status, errorMessage } = await request.json();

    if (!analysisId || !stepNumber || !status) {
      return new Response(JSON.stringify({ 
        error: 'Missing required parameters: analysisId, stepNumber, status' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Update progress using the database function
    await supabaseAdmin.rpc('update_generation_progress', {
      p_analysis_id: analysisId,
      p_step_number: stepNumber,
      p_status: status,
      p_error_message: errorMessage || null
    });

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`Error updating analysis progress: ${error}`);
    
    return new Response(JSON.stringify({
      error: 'Failed to update analysis progress',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
