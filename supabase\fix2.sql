-- ConvertIQ Database Fix 2: Adaptations and Recommendations
-- This file addresses the adaptations and recommendations fields

-- 1. <PERSON><PERSON> function to populate adaptations and recommendations based on analysis data
CREATE OR REPLACE FUNCTION populate_adaptations_recommendations()
RETURNS TRIGGER AS $$
DECLARE
  conversion_score NUMERIC;
  target_audience_text TEXT;
  recommendations_array TEXT[];
  adaptations_array TEXT[];
BEGIN
  conversion_score := NEW.score;
  target_audience_text := NEW.target_audience;
  
  -- Generate recommendations based on conversion score and existing data
  recommendations_array := ARRAY[]::TEXT[];
  adaptations_array := ARRAY[]::TEXT[];
  
  -- Generate recommendations based on conversion score
  IF conversion_score < 4 THEN
    recommendations_array := recommendations_array || ARRAY[
      'Optimize call-to-action button placement and design for better visibility',
      'Add social proof elements like testimonials and customer reviews',
      'Improve value proposition clarity in headlines and subheadings',
      'Reduce form fields to minimize friction in the conversion process'
    ];
  ELSIF conversion_score < 6 THEN
    recommendations_array := recommendations_array || ARRAY[
      'Enhance trust signals with security badges and certifications',
      'Improve page loading speed to reduce bounce rate',
      'Add urgency elements to encourage immediate action',
      'Optimize mobile experience for better mobile conversions'
    ];
  ELSIF conversion_score < 8 THEN
    recommendations_array := recommendations_array || ARRAY[
      'A/B test different headline variations for better engagement',
      'Add live chat or contact options for immediate support',
      'Implement exit-intent popups to capture leaving visitors',
      'Optimize images and content for better visual appeal'
    ];
  ELSE
    recommendations_array := recommendations_array || ARRAY[
      'Fine-tune existing high-performing elements',
      'Implement advanced personalization features',
      'Add upselling opportunities within the conversion flow',
      'Optimize for voice search and emerging technologies'
    ];
  END IF;
  
  -- Generate adaptations based on target audience
  IF target_audience_text IS NOT NULL AND target_audience_text != '' THEN
    IF target_audience_text ILIKE '%business%' OR target_audience_text ILIKE '%b2b%' OR target_audience_text ILIKE '%professional%' THEN
      adaptations_array := adaptations_array || ARRAY[
        'Use professional language and industry-specific terminology',
        'Highlight ROI and business value propositions prominently',
        'Include case studies and enterprise customer testimonials',
        'Add features comparison tables for decision-making support'
      ];
    ELSIF target_audience_text ILIKE '%consumer%' OR target_audience_text ILIKE '%individual%' OR target_audience_text ILIKE '%personal%' THEN
      adaptations_array := adaptations_array || ARRAY[
        'Use conversational and friendly tone in messaging',
        'Focus on personal benefits and lifestyle improvements',
        'Include user-generated content and peer reviews',
        'Simplify technical language for better understanding'
      ];
    ELSIF target_audience_text ILIKE '%young%' OR target_audience_text ILIKE '%millennial%' OR target_audience_text ILIKE '%gen z%' THEN
      adaptations_array := adaptations_array || ARRAY[
        'Incorporate modern design trends and visual elements',
        'Optimize for mobile-first experience',
        'Use social media integration and sharing features',
        'Include video content and interactive elements'
      ];
    ELSIF target_audience_text ILIKE '%senior%' OR target_audience_text ILIKE '%older%' OR target_audience_text ILIKE '%mature%' THEN
      adaptations_array := adaptations_array || ARRAY[
        'Use larger fonts and clear, simple navigation',
        'Provide detailed explanations and support information',
        'Include phone contact options prominently',
        'Focus on trust and security messaging'
      ];
    ELSE
      -- Generic adaptations
      adaptations_array := adaptations_array || ARRAY[
        'Tailor messaging to match audience preferences and pain points',
        'Adjust visual design to appeal to target demographic',
        'Customize content depth based on audience expertise level',
        'Optimize conversion flow for audience behavior patterns'
      ];
    END IF;
  ELSE
    -- Default adaptations when target audience is not specified
    adaptations_array := adaptations_array || ARRAY[
      'Conduct audience research to better understand visitor needs',
      'Implement user behavior tracking for data-driven optimizations',
      'Create multiple landing page variants for different segments',
      'Add feedback collection mechanisms to understand visitor preferences'
    ];
  END IF;
  
  -- Update the recommendations and adaptations if they are currently NULL or empty
  IF NEW.recommendations IS NULL OR NEW.recommendations = 'null'::jsonb OR NEW.recommendations = '[]'::jsonb THEN
    NEW.recommendations := to_jsonb(recommendations_array);
  END IF;
  
  IF NEW.adaptations IS NULL OR NEW.adaptations = 'null'::jsonb OR NEW.adaptations = '[]'::jsonb THEN
    NEW.adaptations := to_jsonb(adaptations_array);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 2. Create trigger to populate adaptations and recommendations
DROP TRIGGER IF EXISTS trigger_populate_adaptations_recommendations ON analyses;
CREATE TRIGGER trigger_populate_adaptations_recommendations
  BEFORE INSERT OR UPDATE OF score, target_audience
  ON analyses
  FOR EACH ROW
  EXECUTE FUNCTION populate_adaptations_recommendations();

-- 3. Update existing analyses that have NULL or empty recommendations/adaptations
UPDATE analyses 
SET 
  recommendations = CASE 
    WHEN recommendations IS NULL OR recommendations = 'null'::jsonb OR recommendations = '[]'::jsonb THEN
      CASE 
        WHEN score < 4 THEN '["Optimize call-to-action button placement and design for better visibility", "Add social proof elements like testimonials and customer reviews", "Improve value proposition clarity in headlines and subheadings", "Reduce form fields to minimize friction in the conversion process"]'::jsonb
        WHEN score < 6 THEN '["Enhance trust signals with security badges and certifications", "Improve page loading speed to reduce bounce rate", "Add urgency elements to encourage immediate action", "Optimize mobile experience for better mobile conversions"]'::jsonb
        WHEN score < 8 THEN '["A/B test different headline variations for better engagement", "Add live chat or contact options for immediate support", "Implement exit-intent popups to capture leaving visitors", "Optimize images and content for better visual appeal"]'::jsonb
        ELSE '["Fine-tune existing high-performing elements", "Implement advanced personalization features", "Add upselling opportunities within the conversion flow", "Optimize for voice search and emerging technologies"]'::jsonb
      END
    ELSE recommendations
  END,
  adaptations = CASE 
    WHEN adaptations IS NULL OR adaptations = 'null'::jsonb OR adaptations = '[]'::jsonb THEN
      CASE 
        WHEN target_audience ILIKE '%business%' OR target_audience ILIKE '%b2b%' OR target_audience ILIKE '%professional%' THEN 
          '["Use professional language and industry-specific terminology", "Highlight ROI and business value propositions prominently", "Include case studies and enterprise customer testimonials", "Add features comparison tables for decision-making support"]'::jsonb
        WHEN target_audience ILIKE '%consumer%' OR target_audience ILIKE '%individual%' OR target_audience ILIKE '%personal%' THEN 
          '["Use conversational and friendly tone in messaging", "Focus on personal benefits and lifestyle improvements", "Include user-generated content and peer reviews", "Simplify technical language for better understanding"]'::jsonb
        WHEN target_audience ILIKE '%young%' OR target_audience ILIKE '%millennial%' OR target_audience ILIKE '%gen z%' THEN 
          '["Incorporate modern design trends and visual elements", "Optimize for mobile-first experience", "Use social media integration and sharing features", "Include video content and interactive elements"]'::jsonb
        WHEN target_audience ILIKE '%senior%' OR target_audience ILIKE '%older%' OR target_audience ILIKE '%mature%' THEN 
          '["Use larger fonts and clear, simple navigation", "Provide detailed explanations and support information", "Include phone contact options prominently", "Focus on trust and security messaging"]'::jsonb
        ELSE 
          '["Tailor messaging to match audience preferences and pain points", "Adjust visual design to appeal to target demographic", "Customize content depth based on audience expertise level", "Optimize conversion flow for audience behavior patterns"]'::jsonb
      END
    ELSE adaptations
  END
WHERE 
  (recommendations IS NULL OR recommendations = 'null'::jsonb OR recommendations = '[]'::jsonb)
  OR 
  (adaptations IS NULL OR adaptations = 'null'::jsonb OR adaptations = '[]'::jsonb);

-- 4. Create view to easily access recommendations and adaptations as text arrays
CREATE OR REPLACE VIEW analysis_recommendations_view AS
SELECT 
  id,
  url,
  title,
  score,
  target_audience,
  -- Convert JSONB arrays to text arrays for easier use in applications
  CASE 
    WHEN jsonb_typeof(recommendations) = 'array' THEN 
      ARRAY(SELECT jsonb_array_elements_text(recommendations))
    ELSE ARRAY[]::TEXT[]
  END as recommendations_array,
  CASE 
    WHEN jsonb_typeof(adaptations) = 'array' THEN 
      ARRAY(SELECT jsonb_array_elements_text(adaptations))
    ELSE ARRAY[]::TEXT[]
  END as adaptations_array,
  created_at,
  updated_at
FROM analyses;

-- 5. Grant permissions
GRANT EXECUTE ON FUNCTION populate_adaptations_recommendations() TO authenticated;
GRANT SELECT ON analysis_recommendations_view TO authenticated;

-- 6. Add helpful comments
COMMENT ON FUNCTION populate_adaptations_recommendations() IS 'Automatically populates recommendations and adaptations based on conversion score and target audience';
COMMENT ON VIEW analysis_recommendations_view IS 'Provides easy access to recommendations and adaptations as text arrays';

-- End of fix2.sql
