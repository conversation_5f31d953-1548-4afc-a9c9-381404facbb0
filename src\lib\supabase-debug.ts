
import fs from 'fs/promises';
import { supabase } from './supabase';

const devMode = true;

interface LighthouseData {
  performance: number;
  accessibility: number;
  seo: number;
  bestPractices: number;
  lcp: number;
  fid: number;
  cls: number;
}

interface WebsiteData {
  uuid: string;
  url: string;
  title: string;
  description: string;
  content: string;
  headings: any[];
  images: any[];
  links: any[];
  screenshot_url: string;
  lighthouse: LighthouseData;
}

interface LogEntry {
  timestamp: string;
  status: 'SUCCESS' | 'FAILURE';
  step: string;
  data?: any;
  error?: string;
}

const log: LogEntry[] = [];

async function wait(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function insertWithRetry(insertFn: () => Promise<any>, stepName: string, data: any, retries = 2, delay = 200) {
  if (!devMode) return;

  try {
    console.log(`[DEV MODE] Inserting ${stepName}...`);
    const result = await insertFn();
    console.log(`[DEV MODE] ${stepName} inserted successfully.`);
    log.push({ timestamp: new Date().toISOString(), status: 'SUCCESS', step: stepName, data });
    return result;
  } catch (error: any) {
    if (retries > 0) {
      console.warn(`[DEV MODE] Failed to insert ${stepName}. Retrying in ${delay}ms...`);
      await wait(delay);
      return insertWithRetry(insertFn, stepName, data, retries - 1, delay * 2.5);
    } else {
      console.error(`[DEV MODE] Failed to insert ${stepName} after multiple retries.`);
      console.error(`[DEV MODE] Error: ${error.message}`);
      console.error(`[DEV MODE] Data:`, data);
      log.push({ timestamp: new Date().toISOString(), status: 'FAILURE', step: stepName, data, error: error.message });
      return null;
    }
  }
}

async function insertScreenshot(uuid: string, screenshot_url: string) {
  const stepName = 'screenshot';
  const data = { screenshot_url };
  return insertWithRetry(async () => 
    await supabase.from('analysis').update(data).eq('uuid', uuid),
    stepName,
    data
  );
}

async function insertMetadata(uuid: string, title: string, description: string) {
    const stepName = 'metadata';
    const data = { title, description };
    return insertWithRetry(async () =>
        await supabase.from('analysis').update(data).eq('uuid', uuid),
        stepName,
        data
    );
}

async function insertContent(uuid: string, content: string, headings: any[], images: any[], links: any[]) {
    const stepName = 'content';
    const data = { content, headings, images, links };
    return insertWithRetry(async () =>
        await supabase.from('analysis').update(data).eq('uuid', uuid),
        stepName,
        data
    );
}

async function insertLighthouseScores(uuid: string, lighthouse: LighthouseData) {
    const stepName = 'lighthouse_scores';
    const data = { lighthouse };
    return insertWithRetry(async () =>
        await supabase.from('analysis').update(data).eq('uuid', uuid),
        stepName,
        data
    );
}

async function writeLogFile() {
  if (!devMode) return;
  const logFilePath = 'dev-insert-log.json';
  try {
    await fs.writeFile(logFilePath, JSON.stringify(log, null, 2));
    console.log(`[DEV MODE] Log file written to ${logFilePath}`);
  } catch (error: any) {
    console.error(`[DEV MODE] Failed to write log file: ${error.message}`);
  }
}

export async function debugInsert(data: WebsiteData) {
  if (!devMode) {
    console.log("Dev mode is disabled. No debug logs will be generated.");
    return;
  }

  console.log(`[DEV MODE] Starting debug insertion for ${data.url}`);

  await insertScreenshot(data.uuid, data.screenshot_url);
  await insertMetadata(data.uuid, data.title, data.description);
  await insertContent(data.uuid, data.content, data.headings, data.images, data.links);
  await insertLighthouseScores(data.uuid, data.lighthouse);

  await writeLogFile();
  console.log(`[DEV MODE] Debug insertion process finished for ${data.url}`);
}

// Example Usage:
/*
const exampleData: WebsiteData = {
  uuid: '123-abc',
  url: 'https://example.com',
  title: 'Example',
  description: 'This is a description.',
  content: '<h1>Main Heading</h1><p>Some content.</p>',
  headings: [{ tag: 'h1', text: 'Main Heading' }],
  images: [{ src: 'img.png', alt: 'An image' }],
  links: [{ href: '/about', text: 'About us' }],
  screenshot_url: 'https://supabase.storage/.../screenshot.png',
  lighthouse: {
    performance: 95,
    accessibility: 100,
    seo: 90,
    bestPractices: 100,
    lcp: 1.2,
    fid: 15,
    cls: 0.01,
  }
};

debugInsert(exampleData);
*/
