/*
  # ConvertIQ Database Schema - Complete Fix (fix4.sql)
  
  This file contains all necessary SQL commands to update the Supabase database
  for the ConvertIQ analysis system. All commands are safe to run multiple times.
  
  ## What this file includes:
  1. Analysis generation progress tracking
  2. AI-generated content storage tables
  3. Performance impact insights
  4. Advanced metrics summary
  5. Enhanced analysis table columns
  6. RLS policies and security
  7. Database functions for progress management
  8. Performance indexes
  
  ## Instructions:
  Run this entire file in the Supabase SQL Editor. All commands use
  IF NOT EXISTS or similar patterns to prevent errors on re-runs.
*/

-- ============================================================================
-- SECTION 1: ANALYSIS TABLE ENHANCEMENTS
-- ============================================================================

-- Add generation tracking columns to analyses table
ALTER TABLE analyses 
ADD COLUMN IF NOT EXISTS generation_status text DEFAULT 'pending' CHECK (generation_status IN ('pending', 'in_progress', 'completed', 'failed')),
ADD COLUMN IF NOT EXISTS generation_started_at timestamptz,
ADD COLUMN IF NOT EXISTS generation_completed_at timestamptz,
ADD COLUMN IF NOT EXISTS current_generation_step text,
ADD COLUMN IF NOT EXISTS total_generation_steps integer DEFAULT 8,
ADD COLUMN IF NOT EXISTS completed_generation_steps integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS generation_error text,
ADD COLUMN IF NOT EXISTS all_content_generated boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS pros_cons_generated boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS lead_insights_generated boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS performance_insights_generated boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS seo_insights_generated boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS chat_context_generated boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS page_summary_enhanced boolean DEFAULT false;

-- ============================================================================
-- SECTION 2: PROGRESS TRACKING TABLE
-- ============================================================================

-- Create analysis generation progress tracking table
CREATE TABLE IF NOT EXISTS analysis_generation_progress (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
  current_step text NOT NULL,
  step_number integer NOT NULL CHECK (step_number >= 1 AND step_number <= 10),
  total_steps integer NOT NULL DEFAULT 8,
  step_description text NOT NULL,
  status text NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
  started_at timestamptz,
  completed_at timestamptz,
  error_message text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(analysis_id, step_number)
);

-- ============================================================================
-- SECTION 3: AI CONTENT STORAGE TABLES
-- ============================================================================

-- Create AI chat context table for pre-generated chat data
CREATE TABLE IF NOT EXISTS ai_chat_context (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
  website_summary text NOT NULL,
  key_metrics jsonb NOT NULL DEFAULT '{}'::jsonb,
  business_context text NOT NULL,
  suggested_prompts jsonb NOT NULL DEFAULT '[]'::jsonb,
  conversation_starters jsonb NOT NULL DEFAULT '[]'::jsonb,
  technical_context jsonb NOT NULL DEFAULT '{}'::jsonb,
  optimization_opportunities jsonb NOT NULL DEFAULT '[]'::jsonb,
  ai_generated boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  UNIQUE(analysis_id)
);

-- Create performance impact insights table
CREATE TABLE IF NOT EXISTS performance_impact_insights (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
  impact_type text NOT NULL CHECK (impact_type IN ('critical', 'opportunity', 'optimization')),
  metric_name text NOT NULL,
  current_value numeric,
  target_value numeric,
  impact_description text NOT NULL,
  conversion_impact text,
  implementation_guide text,
  expected_improvement text,
  effort_level text CHECK (effort_level IN ('Low', 'Medium', 'High')),
  timeline text,
  core_web_vitals_impact text,
  mobile_performance_notes text,
  lighthouse_recommendations jsonb DEFAULT '[]'::jsonb,
  conversion_correlation text,
  technical_debt_assessment text,
  performance_budget_notes text,
  monitoring_recommendations text,
  created_at timestamptz DEFAULT now()
);

-- Create lead qualification insights table
CREATE TABLE IF NOT EXISTS lead_qualification_insights (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  analysis_id uuid REFERENCES analyses(id) ON DELETE CASCADE,
  question_type TEXT,
  question_text TEXT,
  context_explanation TEXT,
  qualification_value TEXT,
  suggested_response TEXT,
  priority_level INT,
  business_impact_score numeric CHECK (business_impact_score >= 0 AND business_impact_score <= 10),
  conversion_relevance text,
  follow_up_questions jsonb DEFAULT '[]'::jsonb,
  objection_handling text,
  qualification_criteria text,
  lead_scoring_weight numeric DEFAULT 1.0,
  created_at timestamptz DEFAULT now()
);

-- Create AI page summaries table
CREATE TABLE IF NOT EXISTS ai_page_summaries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
  page_purpose text,
  key_takeaways jsonb DEFAULT '[]'::jsonb,
  hero_effectiveness text,
  value_proposition_clarity text,
  lead_capture_assessment text,
  user_journey_analysis text,
  business_impact_summary text,
  conversion_assessment text,
  trust_signals_analysis text,
  mobile_experience_notes text,
  competitive_advantages jsonb DEFAULT '[]'::jsonb,
  improvement_priorities jsonb DEFAULT '[]'::jsonb,
  business_model_insights text,
  target_audience_analysis text,
  content_strategy_notes text,
  ai_generated boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  UNIQUE(analysis_id)
);

-- Create comprehensive SEO insights table
CREATE TABLE IF NOT EXISTS ai_seo_insights (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
  meta_optimization_notes text,
  content_seo_assessment text,
  technical_seo_issues jsonb DEFAULT '[]'::jsonb,
  keyword_opportunities jsonb DEFAULT '[]'::jsonb,
  local_seo_recommendations text,
  schema_markup_suggestions jsonb DEFAULT '[]'::jsonb,
  internal_linking_analysis text,
  page_speed_seo_impact text,
  mobile_seo_assessment text,
  competitor_analysis_notes text,
  content_gap_analysis jsonb DEFAULT '[]'::jsonb,
  seo_priority_actions jsonb DEFAULT '[]'::jsonb,
  expected_traffic_impact text,
  ai_generated boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  UNIQUE(analysis_id)
);

-- Create advanced metrics summary table for on-demand generation
CREATE TABLE IF NOT EXISTS advanced_metrics_summary (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
  lighthouse_summary text,
  performance_breakdown jsonb DEFAULT '{}'::jsonb,
  accessibility_insights text,
  best_practices_analysis text,
  seo_technical_summary text,
  mobile_performance_notes text,
  optimization_priorities jsonb DEFAULT '[]'::jsonb,
  business_impact_assessment text,
  technical_debt_analysis text,
  monitoring_recommendations jsonb DEFAULT '[]'::jsonb,
  generated_at timestamptz DEFAULT now(),
  ai_generated boolean DEFAULT true,
  UNIQUE(analysis_id)
);

-- ============================================================================
-- SECTION 4: ENHANCED AI INSIGHTS TABLE
-- ============================================================================

-- Enhance ai_insights table for comprehensive pros/cons storage
ALTER TABLE ai_insights
ADD COLUMN IF NOT EXISTS display_order integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS impact_score numeric CHECK (impact_score >= 0 AND impact_score <= 10),
ADD COLUMN IF NOT EXISTS effort_required text CHECK (effort_required IN ('Low', 'Medium', 'High')),
ADD COLUMN IF NOT EXISTS timeline_estimate text,
ADD COLUMN IF NOT EXISTS success_metrics text,
ADD COLUMN IF NOT EXISTS related_metrics jsonb DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS conversion_impact_estimate text,
ADD COLUMN IF NOT EXISTS technical_requirements text;

-- ============================================================================
-- SECTION 5: ROW LEVEL SECURITY (RLS) POLICIES
-- ============================================================================

-- Enable RLS on new tables
ALTER TABLE analysis_generation_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_chat_context ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_impact_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE lead_qualification_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_page_summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_seo_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE advanced_metrics_summary ENABLE ROW LEVEL SECURITY;

-- User access policies (authenticated users can read their own data)
DO $$
BEGIN
  -- Progress tracking policies
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'analysis_generation_progress' AND policyname = 'Users can read own generation progress') THEN
    CREATE POLICY "Users can read own generation progress"
      ON analysis_generation_progress FOR SELECT
      TO authenticated
      USING (analysis_id IN (SELECT id FROM analyses WHERE user_id = auth.uid()));
  END IF;

  -- Chat context policies
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'ai_chat_context' AND policyname = 'Users can read own chat context') THEN
    CREATE POLICY "Users can read own chat context"
      ON ai_chat_context FOR SELECT
      TO authenticated
      USING (analysis_id IN (SELECT id FROM analyses WHERE user_id = auth.uid()));
  END IF;

  -- Performance insights policies
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'performance_impact_insights' AND policyname = 'Users can read own performance insights') THEN
    CREATE POLICY "Users can read own performance insights"
      ON performance_impact_insights FOR SELECT
      TO authenticated
      USING (analysis_id IN (SELECT id FROM analyses WHERE user_id = auth.uid()));
  END IF;

  -- Lead insights policies
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'lead_qualification_insights' AND policyname = 'Users can read own lead insights') THEN
    CREATE POLICY "Users can read own lead insights"
      ON lead_qualification_insights FOR SELECT
      TO authenticated
      USING (analysis_id IN (SELECT id FROM analyses WHERE user_id = auth.uid()));
  END IF;

  -- Page summaries policies
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'ai_page_summaries' AND policyname = 'Users can read own page summaries') THEN
    CREATE POLICY "Users can read own page summaries"
      ON ai_page_summaries FOR SELECT
      TO authenticated
      USING (analysis_id IN (SELECT id FROM analyses WHERE user_id = auth.uid()));
  END IF;

  -- SEO insights policies
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'ai_seo_insights' AND policyname = 'Users can read own SEO insights') THEN
    CREATE POLICY "Users can read own SEO insights"
      ON ai_seo_insights FOR SELECT
      TO authenticated
      USING (analysis_id IN (SELECT id FROM analyses WHERE user_id = auth.uid()));
  END IF;

  -- Advanced metrics policies
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'advanced_metrics_summary' AND policyname = 'Users can read own advanced metrics') THEN
    CREATE POLICY "Users can read own advanced metrics"
      ON advanced_metrics_summary FOR SELECT
      TO authenticated
      USING (analysis_id IN (SELECT id FROM analyses WHERE user_id = auth.uid()));
  END IF;
END $$;

-- Service role policies (for AI generation processes)
DO $$
BEGIN
  -- Service role can manage all tables
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'analysis_generation_progress' AND policyname = 'Service role can manage generation progress') THEN
    CREATE POLICY "Service role can manage generation progress"
      ON analysis_generation_progress FOR ALL
      TO service_role
      USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'ai_chat_context' AND policyname = 'Service role can manage chat context') THEN
    CREATE POLICY "Service role can manage chat context"
      ON ai_chat_context FOR ALL
      TO service_role
      USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'performance_impact_insights' AND policyname = 'Service role can manage performance insights') THEN
    CREATE POLICY "Service role can manage performance insights"
      ON performance_impact_insights FOR ALL
      TO service_role
      USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'lead_qualification_insights' AND policyname = 'Service role can manage lead insights') THEN
    CREATE POLICY "Service role can manage lead insights"
      ON lead_qualification_insights FOR ALL
      TO service_role
      USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'ai_page_summaries' AND policyname = 'Service role can manage page summaries') THEN
    CREATE POLICY "Service role can manage page summaries"
      ON ai_page_summaries FOR ALL
      TO service_role
      USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'ai_seo_insights' AND policyname = 'Service role can manage SEO insights') THEN
    CREATE POLICY "Service role can manage SEO insights"
      ON ai_seo_insights FOR ALL
      TO service_role
      USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'advanced_metrics_summary' AND policyname = 'Service role can manage advanced metrics') THEN
    CREATE POLICY "Service role can manage advanced metrics"
      ON advanced_metrics_summary FOR ALL
      TO service_role
      USING (true);
  END IF;
END $$;

-- ============================================================================
-- SECTION 6: PERFORMANCE INDEXES
-- ============================================================================

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_generation_progress_analysis_id ON analysis_generation_progress(analysis_id);
CREATE INDEX IF NOT EXISTS idx_generation_progress_status ON analysis_generation_progress(status);
CREATE INDEX IF NOT EXISTS idx_generation_progress_step ON analysis_generation_progress(step_number);
CREATE INDEX IF NOT EXISTS idx_chat_context_analysis_id ON ai_chat_context(analysis_id);
CREATE INDEX IF NOT EXISTS idx_performance_insights_analysis_id ON performance_impact_insights(analysis_id);
CREATE INDEX IF NOT EXISTS idx_performance_insights_type ON performance_impact_insights(impact_type);
CREATE INDEX IF NOT EXISTS idx_lead_insights_analysis_id ON lead_qualification_insights(analysis_id);
CREATE INDEX IF NOT EXISTS idx_page_summaries_analysis_id ON ai_page_summaries(analysis_id);
CREATE INDEX IF NOT EXISTS idx_seo_insights_analysis_id ON ai_seo_insights(analysis_id);
CREATE INDEX IF NOT EXISTS idx_advanced_metrics_analysis_id ON advanced_metrics_summary(analysis_id);
CREATE INDEX IF NOT EXISTS idx_analyses_generation_status ON analyses(generation_status);
CREATE INDEX IF NOT EXISTS idx_analyses_all_content_generated ON analyses(all_content_generated);

-- ============================================================================
-- SECTION 7: DATABASE FUNCTIONS
-- ============================================================================

-- Function to initialize generation progress
CREATE OR REPLACE FUNCTION initialize_generation_progress(p_analysis_id uuid)
RETURNS void AS $$
DECLARE
  generation_steps text[] := ARRAY[
    'Extracting Website Context',
    'Generating Page Summary',
    'Analyzing Pros & Cons',
    'Creating Lead Insights',
    'Analyzing Performance Impact',
    'Generating SEO Insights',
    'Creating Chat Context',
    'Finalizing Analysis'
  ];
  step_descriptions text[] := ARRAY[
    'Extracting and analyzing website content and structure',
    'Generating comprehensive page summary and business insights',
    'Analyzing website strengths and weaknesses for conversion optimization',
    'Creating lead qualification questions and business insights',
    'Analyzing performance metrics and optimization opportunities',
    'Generating SEO recommendations and technical insights',
    'Preparing AI chat context and suggested conversation starters',
    'Completing analysis and marking all content as generated'
  ];
  i integer;
BEGIN
  -- Clear any existing progress
  DELETE FROM analysis_generation_progress WHERE analysis_id = p_analysis_id;

  -- Initialize progress steps
  FOR i IN 1..array_length(generation_steps, 1) LOOP
    INSERT INTO analysis_generation_progress (
      analysis_id,
      current_step,
      step_number,
      total_steps,
      step_description,
      status
    ) VALUES (
      p_analysis_id,
      generation_steps[i],
      i,
      array_length(generation_steps, 1),
      step_descriptions[i],
      'pending'
    );
  END LOOP;

  -- Update analyses table
  UPDATE analyses SET
    generation_status = 'pending',
    current_generation_step = generation_steps[1],
    total_generation_steps = array_length(generation_steps, 1),
    completed_generation_steps = 0,
    all_content_generated = false,
    generation_started_at = now()
  WHERE id = p_analysis_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update generation progress
CREATE OR REPLACE FUNCTION update_generation_progress(
  p_analysis_id uuid,
  p_step_number integer,
  p_status text,
  p_error_message text DEFAULT NULL
)
RETURNS void AS $$
DECLARE
  v_total_steps integer;
  v_next_step text;
BEGIN
  -- Update current step
  UPDATE analysis_generation_progress SET
    status = p_status,
    started_at = CASE WHEN p_status = 'in_progress' THEN now() ELSE started_at END,
    completed_at = CASE WHEN p_status IN ('completed', 'failed') THEN now() ELSE completed_at END,
    error_message = p_error_message,
    updated_at = now()
  WHERE analysis_id = p_analysis_id AND step_number = p_step_number;

  -- Get total steps and next step
  SELECT total_steps INTO v_total_steps
  FROM analysis_generation_progress
  WHERE analysis_id = p_analysis_id
  LIMIT 1;

  SELECT current_step INTO v_next_step
  FROM analysis_generation_progress
  WHERE analysis_id = p_analysis_id AND step_number = p_step_number + 1;

  -- Update analyses table
  IF p_status = 'completed' THEN
    UPDATE analyses SET
      completed_generation_steps = p_step_number,
      current_generation_step = COALESCE(v_next_step, 'Completed'),
      generation_status = CASE
        WHEN p_step_number = v_total_steps THEN 'completed'
        ELSE 'in_progress'
      END,
      generation_completed_at = CASE
        WHEN p_step_number = v_total_steps THEN now()
        ELSE generation_completed_at
      END,
      all_content_generated = CASE
        WHEN p_step_number = v_total_steps THEN true
        ELSE all_content_generated
      END
    WHERE id = p_analysis_id;
  ELSIF p_status = 'failed' THEN
    UPDATE analyses SET
      generation_status = 'failed',
      generation_error = p_error_message
    WHERE id = p_analysis_id;
  ELSIF p_status = 'in_progress' THEN
    UPDATE analyses SET
      generation_status = 'in_progress',
      current_generation_step = (
        SELECT current_step FROM analysis_generation_progress
        WHERE analysis_id = p_analysis_id AND step_number = p_step_number
      )
    WHERE id = p_analysis_id;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to mark specific content as generated
CREATE OR REPLACE FUNCTION mark_content_generated(
  p_analysis_id uuid,
  p_content_type text
)
RETURNS void AS $$
BEGIN
  CASE p_content_type
    WHEN 'pros_cons' THEN
      UPDATE analyses SET pros_cons_generated = true WHERE id = p_analysis_id;
    WHEN 'lead_insights' THEN
      UPDATE analyses SET lead_insights_generated = true WHERE id = p_analysis_id;
    WHEN 'performance_insights' THEN
      UPDATE analyses SET performance_insights_generated = true WHERE id = p_analysis_id;
    WHEN 'seo_insights' THEN
      UPDATE analyses SET seo_insights_generated = true WHERE id = p_analysis_id;
    WHEN 'chat_context' THEN
      UPDATE analyses SET chat_context_generated = true WHERE id = p_analysis_id;
    WHEN 'page_summary' THEN
      UPDATE analyses SET page_summary_enhanced = true WHERE id = p_analysis_id;
  END CASE;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- SECTION 8: GRANT PERMISSIONS
-- ============================================================================

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION initialize_generation_progress(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION update_generation_progress(uuid, integer, text, text) TO service_role;
GRANT EXECUTE ON FUNCTION mark_content_generated(uuid, text) TO service_role;

-- ============================================================================
-- SECTION 9: HELPFUL COMMENTS AND DOCUMENTATION
-- ============================================================================

-- Add helpful comments to tables
COMMENT ON TABLE analysis_generation_progress IS 'Tracks real-time progress of AI content generation for each analysis';
COMMENT ON TABLE ai_chat_context IS 'Stores pre-generated chat context and conversation starters for AI chat interface';
COMMENT ON TABLE performance_impact_insights IS 'AI-generated performance analysis and optimization recommendations';
COMMENT ON TABLE lead_qualification_insights IS 'AI-generated lead qualification questions and business insights';
COMMENT ON TABLE ai_page_summaries IS 'Comprehensive AI-generated page analysis and business insights';
COMMENT ON TABLE ai_seo_insights IS 'Comprehensive SEO analysis and recommendations generated by AI';
COMMENT ON TABLE advanced_metrics_summary IS 'On-demand advanced performance metrics analysis and recommendations';

-- Add comments to functions
COMMENT ON FUNCTION initialize_generation_progress(uuid) IS 'Initializes progress tracking for a new analysis generation process';
COMMENT ON FUNCTION update_generation_progress(uuid, integer, text, text) IS 'Updates progress status for a specific generation step';
COMMENT ON FUNCTION mark_content_generated(uuid, text) IS 'Marks specific content types as generated in the analyses table';

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE 'ConvertIQ Database Schema Update Complete!';
  RAISE NOTICE 'All tables, indexes, policies, and functions have been created or updated.';
  RAISE NOTICE 'The system is now ready for comprehensive AI analysis generation.';
END $$;
