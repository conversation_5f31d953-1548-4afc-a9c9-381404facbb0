<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../types/supabase';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

type SEOIssue = Database['public']['Tables']['seo_issues']['Row'];

const seoIssues = ref<SEOIssue[]>([]);
const loading = ref(true);

onMounted(async () => {
  await loadSEOIssues();
});

const loadSEOIssues = async () => {
  try {
    const { data, error } = await supabase
      .from('seo_issues')
      .select('*')
      .eq('analysis_id', props.analysis.id)
      .order('severity_score', { ascending: false });

    if (error) throw error;
    seoIssues.value = data || [];
  } catch (error) {
    console.error('Error loading SEO issues:', error);
  } finally {
    loading.value = false;
  }
};

const seoScoreColor = computed(() => {
  const score = props.analysis.seo_score || 0;
  if (score >= 90) return 'text-green-600';
  if (score >= 80) return 'text-blue-600';
  if (score >= 70) return 'text-yellow-600';
  if (score >= 60) return 'text-orange-600';
  return 'text-red-600';
});

const seoData = computed(() => {
  return props.analysis.seo_data as any || {};
});

const groupedIssues = computed(() => {
  const groups: Record<string, SEOIssue[]> = {
    critical: [],
    warning: [],
    info: []
  };

  seoIssues.value.forEach(issue => {
    // Group by severity_score: 4-5=critical, 2-3=warning, 1=info
    if (issue.severity_score >= 4) {
      groups.critical.push(issue);
    } else if (issue.severity_score >= 2) {
      groups.warning.push(issue);
    } else {
      groups.info.push(issue);
    }
  });

  return groups;
});

const getSeverityIcon = (severityScore: number) => {
  if (severityScore >= 4) {
    return 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
  } else if (severityScore >= 2) {
    return 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z';
  } else {
    return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
  }
};

const getSeverityColor = (severityScore: number) => {
  if (severityScore >= 4) {
    return 'text-red-500 bg-red-50 border-red-200';
  } else if (severityScore >= 2) {
    return 'text-yellow-600 bg-yellow-50 border-yellow-200';
  } else {
    return 'text-blue-500 bg-blue-50 border-blue-200';
  }
};

const getSeverityLabel = (severityScore: number) => {
  if (severityScore === 5) return 'Critical';
  if (severityScore === 4) return 'High';
  if (severityScore === 3) return 'Medium';
  if (severityScore === 2) return 'Low';
  return 'Info';
};

const pros = computed(() => {
  return seoData.value.pros || [
    'Clean URL structure',
    'Fast loading speed',
    'Mobile-friendly design',
    'Proper heading hierarchy'
  ];
});

const cons = computed(() => {
  return seoData.value.cons || [
    'Missing meta description',
    'Limited structured data',
    'Could improve title tags',
    'Missing alt text on some images'
  ];
});
</script>

<template>
  <div class="space-y-6">
    <!-- SEO Overview -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center mb-4">
        <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
        <h3 class="text-lg font-semibold text-gray-900">SEO Analysis</h3>
      </div>
      <p class="text-gray-600 mb-6">Search engine optimization and technical SEO analysis</p>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- SEO Score -->
        <div class="space-y-4">
          <div>
            <p class="text-sm text-gray-500 mb-1">SEO Score</p>
            <div class="flex items-center space-x-3">
              <span class="text-3xl font-bold" :class="seoScoreColor">
                {{ analysis.seo_score || 0 }}
              </span>
              <span class="text-gray-500">/100</span>
            </div>
          </div>

          <!-- Page Info -->
          <div class="space-y-3">
            <div v-if="seoData.title" class="p-3 bg-gray-50 rounded-lg">
              <h4 class="text-sm font-medium text-gray-900 mb-1">Page Title</h4>
              <p class="text-sm text-gray-700">{{ seoData.title }}</p>
            </div>
            
            <div v-if="seoData.metaDescription" class="p-3 bg-gray-50 rounded-lg">
              <h4 class="text-sm font-medium text-gray-900 mb-1">Meta Description</h4>
              <p class="text-sm text-gray-700">{{ seoData.metaDescription }}</p>
            </div>
            
            <div v-else class="p-3 bg-red-50 rounded-lg border border-red-200">
              <h4 class="text-sm font-medium text-red-900 mb-1">Meta Description</h4>
              <p class="text-sm text-red-700">Missing - Add a meta description to improve search results</p>
            </div>
          </div>
        </div>

        <!-- Issues Summary -->
        <div class="space-y-4">
          <div class="grid grid-cols-3 gap-3">
            <div class="text-center p-3 bg-red-50 rounded-lg border border-red-200">
              <div class="text-lg font-bold text-red-600">{{ groupedIssues.critical.length }}</div>
              <div class="text-xs text-red-600">Critical</div>
            </div>
            <div class="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <div class="text-lg font-bold text-yellow-600">{{ groupedIssues.warning.length }}</div>
              <div class="text-xs text-yellow-600">Warnings</div>
            </div>
            <div class="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div class="text-lg font-bold text-blue-600">{{ groupedIssues.info.length }}</div>
              <div class="text-xs text-blue-600">Info</div>
            </div>
          </div>

          <!-- Heading Structure -->
          <div v-if="seoData.headingStructure" class="p-3 bg-gray-50 rounded-lg">
            <h4 class="text-sm font-medium text-gray-900 mb-2">Heading Structure</h4>
            <div class="space-y-1 max-h-32 overflow-y-auto">
              <div 
                v-for="(heading, index) in seoData.headingStructure.slice(0, 5)" 
                :key="index"
                class="text-xs text-gray-600"
                :style="{ paddingLeft: `${(heading.level - 1) * 12}px` }"
              >
                H{{ heading.level }}: {{ heading.text.substring(0, 50) }}{{ heading.text.length > 50 ? '...' : '' }}
              </div>
              <div v-if="seoData.headingStructure.length > 5" class="text-xs text-gray-500 italic">
                +{{ seoData.headingStructure.length - 5 }} more headings
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- SEO Issues -->
    <div v-if="!loading && seoIssues.length > 0" class="bg-white rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">SEO Issues & Recommendations</h3>
      
      <div class="space-y-4">
        <div v-for="issue in seoIssues" :key="issue.id" class="border rounded-lg p-4" :class="getSeverityColor(issue.severity_score)">
          <div class="flex items-start">
            <svg class="w-5 h-5 mr-3 mt-0.5" :class="getSeverityColor(issue.severity_score).split(' ')[0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getSeverityIcon(issue.severity_score)" />
            </svg>
            <div class="flex-1">
              <div class="flex items-center justify-between mb-2">
                <h4 class="font-medium text-gray-900">{{ issue.issue }}</h4>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" :class="getSeverityColor(issue.severity_score)">
                  {{ getSeverityLabel(issue.severity_score) }} ({{ issue.severity_score }}/5)
                </span>
              </div>
              <p class="text-sm text-gray-700 mb-2">{{ issue.recommendation }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-else-if="loading" class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="animate-pulse space-y-4">
        <div class="h-4 bg-gray-200 rounded w-1/4"></div>
        <div class="space-y-3">
          <div class="h-16 bg-gray-200 rounded"></div>
          <div class="h-16 bg-gray-200 rounded"></div>
          <div class="h-16 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="bg-white rounded-lg border border-gray-200 p-6 text-center">
      <svg class="w-12 h-12 text-gray-300 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No SEO issues found</h3>
      <p class="text-gray-500">SEO analysis is still processing or no issues were detected.</p>
    </div>
  </div>
</template>