<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { Database } from '../../types/supabase';
import { generateAIProsConsAnalysis } from '../../lib/enhanced-ai-analyzer';
import { CheckCircle, XCircle, TrendingUp, Alert<PERSON><PERSON>gle, Lightbulb, Target } from 'lucide-vue-next';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

const isVisible = ref(false);
const aiAnalysis = ref<{
  strengths: Array<{ insight: string; evidence: string; impact: string }>;
  weaknesses: Array<{ insight: string; evidence: string; impact: string; recommendation: string }>;
  overallAssessment: string;
} | null>(null);
const isLoadingAI = ref(false);
const aiError = ref<string | null>(null);

onMounted(async () => {
  // Trigger animation after component mounts
  setTimeout(() => {
    isVisible.value = true;
  }, 100);

  // Load AI analysis
  await loadAIAnalysis();
});

async function loadAIAnalysis() {
  if (!props.analysis) return;

  isLoadingAI.value = true;
  aiError.value = null;

  try {
    aiAnalysis.value = await generateAIProsConsAnalysis(props.analysis);
  } catch (error) {
    console.error('Error loading AI analysis:', error);
    aiError.value = 'Failed to load AI analysis. Falling back to basic analysis.';
  } finally {
    isLoadingAI.value = false;
  }
}

const allPros = computed(() => {
  // Prioritize AI-generated strengths when available
  if (aiAnalysis.value && !isLoadingAI.value) {
    return aiAnalysis.value.strengths.map(strength => ({
      text: strength.insight,
      evidence: strength.evidence,
      impact: strength.impact
    }));
  }

  // Fallback to existing data while AI loads or if AI fails
  const conversionPros = Array.isArray(props.analysis.pros) ? props.analysis.pros : [];
  const seoPros = props.analysis.seo_data ? (props.analysis.seo_data as any).pros || [] : [];
  const performancePros = [];

  // Add performance-based pros (fallback logic)
  if (props.analysis.performance_score && props.analysis.performance_score >= 80) {
    performancePros.push('Good page performance');
  }
  if (props.analysis.lcp_score && props.analysis.lcp_score <= 2.5) {
    performancePros.push('Fast content loading');
  }
  if (props.analysis.cls_score && props.analysis.cls_score <= 0.1) {
    performancePros.push('Stable visual layout');
  }

  // Combine and deduplicate
  const combined = [...conversionPros, ...seoPros, ...performancePros];
  return [...new Set(combined)].slice(0, 8).map(text => ({
    text,
    evidence: undefined,
    impact: undefined
  })); // Limit to 8 items
});

const allCons = computed(() => {
  // Prioritize AI-generated weaknesses when available
  if (aiAnalysis.value && !isLoadingAI.value) {
    return aiAnalysis.value.weaknesses.map(weakness => ({
      text: weakness.insight,
      evidence: weakness.evidence,
      impact: weakness.impact,
      recommendation: weakness.recommendation
    }));
  }

  // Fallback to existing data while AI loads or if AI fails
  const conversionCons = Array.isArray(props.analysis.cons) ? props.analysis.cons : [];
  const seoCons = props.analysis.seo_data ? (props.analysis.seo_data as any).cons || [] : [];
  const performanceCons = [];

  // Add performance-based cons (fallback logic)
  if (props.analysis.performance_score && props.analysis.performance_score < 70) {
    performanceCons.push('Poor page performance affecting user experience');
  }
  if (props.analysis.lcp_score && props.analysis.lcp_score > 4.0) {
    performanceCons.push('Slow content loading may cause user drop-off');
  }
  if (props.analysis.fid_score && props.analysis.fid_score > 300) {
    performanceCons.push('Delayed response to user interactions');
  }
  if (props.analysis.cls_score && props.analysis.cls_score > 0.25) {
    performanceCons.push('Unstable layout causing poor user experience');
  }

  // Combine and deduplicate
  const combined = [...conversionCons, ...seoCons, ...performanceCons];
  return [...new Set(combined)].slice(0, 8).map(text => ({
    text,
    evidence: undefined,
    impact: undefined,
    recommendation: undefined
  })); // Limit to 8 items
});

const overallScore = computed(() => {
  const scores = [
    props.analysis.score || 0,
    props.analysis.performance_score || 0,
    props.analysis.seo_score || 0
  ].filter(score => score > 0);
  
  if (scores.length === 0) return 0;
  return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
});

const getOverallGrade = computed(() => {
  const score = overallScore.value;
  if (score >= 90) return { grade: 'A', color: 'text-green-600', bg: 'bg-green-50' };
  if (score >= 80) return { grade: 'B', color: 'text-blue-600', bg: 'bg-blue-50' };
  if (score >= 70) return { grade: 'C', color: 'text-yellow-600', bg: 'bg-yellow-50' };
  if (score >= 60) return { grade: 'D', color: 'text-orange-600', bg: 'bg-orange-50' };
  return { grade: 'F', color: 'text-red-600', bg: 'bg-red-50' };
});
</script>

<template>
  <div 
    class="animate-fade-in"
  >
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <h3 class="text-lg font-semibold text-gray-900">Pros and Cons</h3>
          <!-- AI Loading Indicator -->
          <div v-if="isLoadingAI" class="ml-3 flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span class="ml-2 text-xs text-gray-500">Generating AI insights...</span>
          </div>
          <!-- AI Error Indicator -->
          <div v-else-if="aiError" class="ml-3 flex items-center">
            <AlertTriangle class="w-4 h-4 text-yellow-500 mr-1" />
            <span class="text-xs text-yellow-600">Using fallback analysis</span>
          </div>
          <!-- AI Success Indicator -->
          <div v-else-if="aiAnalysis" class="ml-3 flex items-center">
            <CheckCircle class="w-4 h-4 text-green-500 mr-1" />
            <span class="text-xs text-green-600">AI-powered insights</span>
          </div>
        </div>
        
        <!-- Overall Grade -->
        <div class="flex items-center space-x-3">
          <div class="text-right">
            <p class="text-sm text-gray-500">Overall Grade</p>
          </div>
          <div
            class="w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold"
            :class="[getOverallGrade.color, getOverallGrade.bg]"
          >
            {{ getOverallGrade.grade }}
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Pros -->
        <div class="space-y-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h4 class="font-medium text-green-900">Strengths</h4>
          </div>
          
          <div class="space-y-2">
            <div
              v-for="(pro, index) in allPros"
              :key="`pro-${index}`"
              class="flex items-start p-3 bg-green-50 rounded-lg border border-green-200 transition-all duration-300 ease-out transform"
            >
              <CheckCircle class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              <div class="flex-1">
                <p class="text-sm font-medium text-green-800">{{ pro.text || pro }}</p>
                <p v-if="pro.evidence" class="text-xs text-green-600 mt-1">
                  <strong>Evidence:</strong> {{ pro.evidence }}
                </p>
                <p v-if="pro.impact" class="text-xs text-green-600 mt-1">
                  <strong>Impact:</strong> {{ pro.impact }}
                </p>
              </div>
            </div>
            
            <div v-if="allPros.length === 0" class="text-center py-8 text-gray-500">
              <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p class="text-sm">No strengths identified yet</p>
            </div>
          </div>
        </div>

        <!-- Cons -->
        <div class="space-y-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h4 class="font-medium text-red-900">Areas for Improvement</h4>
          </div>
          
          <div class="space-y-2">
            <div
              v-for="(con, index) in allCons"
              :key="`con-${index}`"
              class="flex items-start p-3 bg-red-50 rounded-lg border border-red-200 transition-all duration-300 ease-out transform"
            >
              <XCircle class="w-4 h-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
              <div class="flex-1">
                <p class="text-sm font-medium text-red-800">{{ con.text || con }}</p>
                <p v-if="con.evidence" class="text-xs text-red-600 mt-1">
                  <strong>Evidence:</strong> {{ con.evidence }}
                </p>
                <p v-if="con.impact" class="text-xs text-red-600 mt-1">
                  <strong>Impact:</strong> {{ con.impact }}
                </p>
                <p v-if="con.recommendation" class="text-xs text-red-700 mt-2 p-2 bg-red-100 rounded">
                  <Lightbulb class="w-3 h-3 inline mr-1" />
                  <strong>Recommendation:</strong> {{ con.recommendation }}
                </p>
              </div>
            </div>
            
            <div v-if="allCons.length === 0" class="text-center py-8 text-gray-500">
              <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p class="text-sm">No issues identified</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Summary -->
      <div class="mt-6 pt-6 border-t border-gray-200">
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="font-medium text-gray-900 mb-2">Summary</h4>
          <p class="text-sm text-gray-700">
            This analysis combines conversion optimization, performance metrics, and SEO factors to provide a comprehensive view of your page's effectiveness. 
            Focus on addressing the areas for improvement to maximize your conversion potential.
          </p>
        </div>
      </div>

      <!-- AI Overall Assessment -->
      <div v-if="aiAnalysis?.overallAssessment && !isLoadingAI" class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div class="flex items-start">
          <Target class="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
          <div>
            <h4 class="font-medium text-blue-900 mb-2">Overall Assessment</h4>
            <p class="text-sm text-blue-800">{{ aiAnalysis.overallAssessment }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Additional animation styles if needed */
</style>
