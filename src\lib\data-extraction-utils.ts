/**
 * Data extraction utilities for preparing website data for AI analysis
 * These utilities extract and format actual website content, performance data,
 * and audit results to provide rich context for AI-generated insights.
 */

import type { Database } from '../types/supabase';
import type { WebsiteContext } from './ai-prompt-templates';

type Analysis = Database['public']['Tables']['analyses']['Row'];

/**
 * Extract comprehensive website context from analysis data
 */
export function extractWebsiteContext(analysis: Analysis): WebsiteContext {
  const lighthouseData = analysis.lighthouse_data ? 
    (typeof analysis.lighthouse_data === 'string' ? 
      JSON.parse(analysis.lighthouse_data) : 
      analysis.lighthouse_data) : null;

  const seoData = analysis.seo_data ? 
    (typeof analysis.seo_data === 'string' ? 
      JSON.parse(analysis.seo_data) : 
      analysis.seo_data) : null;

  // Extract scraped content from lighthouse data or analysis
  const scrapedContent = lighthouseData?.scrapedData || extractScrapedDataFromAnalysis(analysis);

  return {
    url: analysis.url || '',
    htmlContent: scrapedContent?.content || '',
    title: analysis.title || scrapedContent?.title || '',
    metaDescription: scrapedContent?.metadata?.description,
    headings: scrapedContent?.headings || extractHeadingsFromContent(scrapedContent?.content || ''),
    images: scrapedContent?.images || [],
    links: scrapedContent?.links || [],
    performanceData: {
      performanceScore: analysis.performance_score,
      performanceGrade: analysis.performance_grade,
      lcpScore: analysis.lcp_score,
      fidScore: analysis.fid_score,
      clsScore: analysis.cls_score,
      coreWebVitals: {
        lcp: analysis.lcp_score,
        fid: analysis.fid_score,
        cls: analysis.cls_score
      }
    },
    lighthouseData: lighthouseData?.performanceData,
    seoData: seoData,
    scrapedContent: scrapedContent
  };
}

/**
 * Extract scraped data from analysis object
 */
function extractScrapedDataFromAnalysis(analysis: Analysis): any {
  try {
    // Try to extract from analysis_data field
    

    // Try to extract from lighthouse_data
    if (analysis.lighthouse_data) {
      const lighthouseData = typeof analysis.lighthouse_data === 'string' ? 
        JSON.parse(analysis.lighthouse_data) : 
        analysis.lighthouse_data;
      
      if (lighthouseData.scrapedData) {
        return lighthouseData.scrapedData;
      }
    }

    return null;
  } catch (error) {
    console.error('Error extracting scraped data:', error);
    return null;
  }
}

/**
 * Extract headings from HTML content
 */
function extractHeadingsFromContent(htmlContent: string): string[] {
  if (!htmlContent) return [];
  
  try {
    const headingMatches = htmlContent.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi);
    if (!headingMatches) return [];
    
    return headingMatches
      .map(match => {
        // Remove HTML tags and decode entities
        return match.replace(/<[^>]*>/g, '').trim();
      })
      .filter(heading => heading.length > 0)
      .slice(0, 10); // Limit to first 10 headings
  } catch (error) {
    console.error('Error extracting headings:', error);
    return [];
  }
}

/**
 * Extract performance insights from Lighthouse data
 */
export function extractPerformanceInsights(context: WebsiteContext): {
  coreWebVitalsIssues: string[];
  performanceOpportunities: string[];
  specificMetrics: any[];
} {
  const insights = {
    coreWebVitalsIssues: [] as string[],
    performanceOpportunities: [] as string[],
    specificMetrics: [] as any[]
  };

  if (!context.performanceData) return insights;

  const { lcpScore, fidScore, clsScore, performanceScore } = context.performanceData;

  // Analyze Core Web Vitals
  if (lcpScore && lcpScore > 2.5) {
    insights.coreWebVitalsIssues.push(`LCP (${lcpScore.toFixed(2)}s) exceeds 2.5s threshold`);
  }
  
  if (fidScore && fidScore > 100) {
    insights.coreWebVitalsIssues.push(`FID (${fidScore}ms) exceeds 100ms threshold`);
  }
  
  if (clsScore && clsScore > 0.1) {
    insights.coreWebVitalsIssues.push(`CLS (${clsScore.toFixed(3)}) exceeds 0.1 threshold`);
  }

  // Extract specific metrics
  insights.specificMetrics = [
    { name: 'Performance Score', value: performanceScore, unit: '/100' },
    { name: 'Largest Contentful Paint', value: lcpScore, unit: 's' },
    { name: 'First Input Delay', value: fidScore, unit: 'ms' },
    { name: 'Cumulative Layout Shift', value: clsScore, unit: '' }
  ].filter(metric => metric.value !== null && metric.value !== undefined);

  return insights;
}

/**
 * Extract SEO insights from audit data
 */
export function extractSEOInsights(context: WebsiteContext): {
  technicalIssues: string[];
  contentIssues: string[];
  opportunities: string[];
} {
  const insights = {
    technicalIssues: [] as string[],
    contentIssues: [] as string[],
    opportunities: [] as string[]
  };

  if (!context.seoData) return insights;

  // Analyze technical SEO issues
  if (!context.metaDescription) {
    insights.technicalIssues.push('Missing meta description');
  }

  if (context.title && context.title.length > 60) {
    insights.technicalIssues.push('Title tag exceeds 60 characters');
  }

  if (context.headings.length === 0) {
    insights.contentIssues.push('No heading structure detected');
  }

  // Check for H1 tags
  const h1Count = context.htmlContent.match(/<h1[^>]*>/gi)?.length || 0;
  if (h1Count === 0) {
    insights.contentIssues.push('Missing H1 tag');
  } else if (h1Count > 1) {
    insights.contentIssues.push('Multiple H1 tags detected');
  }

  return insights;
}

/**
 * Extract business context from page content
 */
export function extractBusinessContext(context: WebsiteContext): {
  businessType: string;
  industry: string;
  targetAudience: string;
  valueProposition: string;
} {
  const content = context.htmlContent.toLowerCase();
  const title = context.title.toLowerCase();
  
  // Detect business type
  let businessType = 'Unknown';
  if (content.includes('shop') || content.includes('buy') || content.includes('cart')) {
    businessType = 'E-commerce';
  } else if (content.includes('saas') || content.includes('software') || content.includes('app')) {
    businessType = 'SaaS';
  } else if (content.includes('service') || content.includes('consulting')) {
    businessType = 'Service Business';
  } else if (content.includes('blog') || content.includes('article')) {
    businessType = 'Content/Media';
  }

  // Extract value proposition from headings
  const valueProposition = context.headings[0] || context.title || 'Value proposition not clearly defined';

  // Detect target audience from content
  let targetAudience = 'General audience';
  if (content.includes('business') || content.includes('enterprise')) {
    targetAudience = 'Business/Enterprise';
  } else if (content.includes('developer') || content.includes('technical')) {
    targetAudience = 'Developers/Technical';
  } else if (content.includes('small business') || content.includes('startup')) {
    targetAudience = 'Small Business/Startups';
  }

  return {
    businessType,
    industry: businessType, // Simplified for now
    targetAudience,
    valueProposition
  };
}

/**
 * Extract conversion elements from page content
 */
export function extractConversionElements(context: WebsiteContext): {
  ctaButtons: string[];
  forms: string[];
  trustSignals: string[];
  socialProof: string[];
} {
  const content = context.htmlContent;
  
  // Extract CTA button text
  const ctaMatches = content.match(/<button[^>]*>(.*?)<\/button>/gi) || [];
  const linkMatches = content.match(/<a[^>]*class="[^"]*btn[^"]*"[^>]*>(.*?)<\/a>/gi) || [];
  
  const ctaButtons = [...ctaMatches, ...linkMatches]
    .map(match => match.replace(/<[^>]*>/g, '').trim())
    .filter(text => text.length > 0)
    .slice(0, 5);

  // Detect forms
  const formMatches = content.match(/<form[^>]*>[\s\S]*?<\/form>/gi) || [];
  const forms = formMatches.map((form, index) => `Form ${index + 1} detected`);

  // Detect trust signals
  const trustSignals = [];
  if (content.includes('ssl') || content.includes('secure')) trustSignals.push('Security mentions');
  if (content.includes('guarantee') || content.includes('warranty')) trustSignals.push('Guarantees');
  if (content.includes('certified') || content.includes('verified')) trustSignals.push('Certifications');

  // Detect social proof
  const socialProof = [];
  if (content.includes('testimonial') || content.includes('review')) socialProof.push('Customer testimonials');
  if (content.includes('customer') && content.includes('logo')) socialProof.push('Customer logos');
  if (content.match(/\d+[,\d]*\s*(customer|user|client)/i)) socialProof.push('Customer count statistics');

  return {
    ctaButtons,
    forms,
    trustSignals,
    socialProof
  };
}
