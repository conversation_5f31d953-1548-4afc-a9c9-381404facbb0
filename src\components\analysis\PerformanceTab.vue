<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { supabase } from '../../lib/supabase';
import { generateAIPerformanceInsights } from '../../lib/ai-contextual-analyzer';
import type { Database } from '../../types/supabase';
import { CORE_WEB_VITALS_THRESHOLDS, evaluateMetric } from '../../lib/performance-analyzer';
import { Info, HelpCircle, TrendingUp } from 'lucide-vue-next';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

const aiPerformanceInsights = ref<{
  primaryIssue: {
    title: string;
    description: string;
    evidence: string;
    conversionImpact: string;
  };
  keyFindings: Array<{
    metric: string;
    currentValue: string;
    targetValue: string;
    impact: string;
  }>;
  recommendations: Array<{
    title: string;
    description: string;
    expectedImprovement: string;
    priority: string;
  }>;
  businessImpact: string;
} | null>(null);

const isLoadingAI = ref(false);
const aiError = ref<string | null>(null);
const showAdvancedMetrics = ref(false);
const showTooltip = ref<string | null>(null);
const advancedSummary = ref<any>(null);
const isLoadingAdvancedSummary = ref(false);
const advancedSummaryError = ref<string | null>(null);

onMounted(async () => {
  await loadAIPerformanceInsights();
});

async function loadAIPerformanceInsights() {
  if (!props.analysis) return;

  console.log('Loading performance insights for analysis:', props.analysis.id);

  // First, check if AI performance insights already exist in database from sequential generation
  const { data: existingInsights, error: fetchError } = await supabase
    .from('performance_impact_insights')
    .select('*')
    .eq('analysis_id', props.analysis.id)
    .order('created_at', { ascending: true });

  if (fetchError) {
    console.error('Error fetching performance insights:', fetchError);
  }

  if (existingInsights && existingInsights.length > 0) {
    console.log('Found existing performance insights:', existingInsights.length);

    // Use existing insights to construct the response
    const criticalInsight = existingInsights.find(insight => insight.impact_type === 'critical');
    const optimizationInsights = existingInsights.filter(insight => insight.impact_type === 'optimization');
    const opportunityInsights = existingInsights.filter(insight => insight.impact_type === 'opportunity');

    // Construct primary issue from critical insight or first insight
    const primaryInsight = criticalInsight || existingInsights[0];

    aiPerformanceInsights.value = {
      primaryIssue: {
        title: primaryInsight?.metric_name || 'Performance Analysis Complete',
        description: primaryInsight?.impact_description || 'Performance metrics analyzed using AI insights',
        evidence: primaryInsight?.implementation_guide || 'Based on comprehensive performance analysis',
        conversionImpact: primaryInsight?.conversion_impact || 'Performance optimization can improve conversions'
      },
      keyFindings: optimizationInsights.length > 0 ? optimizationInsights.map(insight => ({
        metric: insight.metric_name,
        currentValue: insight.current_value?.toString() || 'Current',
        targetValue: insight.target_value?.toString() || 'Target',
        impact: insight.impact_description
      })) : [{
        metric: 'Performance Analysis',
        currentValue: (props.analysis.performance_score || 0).toString(),
        targetValue: '90+',
        impact: 'AI-generated performance insights available'
      }],
      recommendations: opportunityInsights.length > 0 ? opportunityInsights.map(insight => ({
        title: insight.metric_name,
        description: insight.implementation_guide || insight.impact_description,
        expectedImprovement: insight.expected_improvement || 'Performance improvement expected',
        priority: insight.effort_level || 'Medium'
      })) : [{
        title: 'Optimize Performance',
        description: 'Review AI-generated performance recommendations',
        expectedImprovement: 'Better user experience and conversions',
        priority: 'High'
      }],
      businessImpact: existingInsights[0]?.conversion_impact || 'AI-powered performance analysis completed'
    };

    console.log('Performance insights loaded from database');
    return;
  }

  console.log('No existing performance insights found, checking if analysis is still generating...');

  // Check if the analysis is still being generated
  if ((props.analysis as any).generation_status === 'in_progress' || (props.analysis as any).generation_status === 'pending') {
    console.log('Analysis still generating, showing placeholder');
    aiPerformanceInsights.value = {
      primaryIssue: {
        title: 'Performance Analysis In Progress',
        description: 'AI is currently analyzing your website performance metrics',
        evidence: 'Analysis will be available shortly',
        conversionImpact: 'Comprehensive insights are being generated'
      },
      keyFindings: [],
      recommendations: [],
      businessImpact: 'Performance analysis is being generated by AI'
    };
    return;
  }

  // Only generate new insights if no existing data and generation is complete
  console.log('Generating new AI performance insights...');
  isLoadingAI.value = true;
  aiError.value = null;

  try {
    aiPerformanceInsights.value = await generateAIPerformanceInsights(props.analysis);
    console.log('New AI performance insights generated');
  } catch (error) {
    console.error('Error generating AI performance insights:', error);
    aiError.value = 'Failed to generate AI insights. Using fallback analysis.';

    // Fallback analysis
    aiPerformanceInsights.value = {
      primaryIssue: {
        title: 'Performance Analysis Complete',
        description: 'Performance metrics have been analyzed using available data',
        evidence: 'Based on Core Web Vitals and Lighthouse data',
        conversionImpact: 'Performance optimization can improve user experience and conversions'
      },
      keyFindings: [
        {
          metric: 'Overall Performance',
          currentValue: (props.analysis.performance_score || 0).toString(),
          targetValue: '90+',
          impact: 'Higher performance scores correlate with better user experience'
        }
      ],
      recommendations: [
        {
          title: 'Optimize Core Web Vitals',
          description: 'Focus on improving LCP, FID, and CLS metrics',
          expectedImprovement: 'Better user experience and search rankings',
          priority: 'High'
        }
      ],
      businessImpact: 'Performance optimization can improve user experience and conversions'
    };
  } finally {
    isLoadingAI.value = false;
  }
}

const performanceGradeColor = computed(() => {
  switch (props.analysis.performance_grade) {
    case 'A': return 'bg-green-500 text-white';
    case 'B': return 'bg-blue-500 text-white';
    case 'C': return 'bg-yellow-500 text-white';
    case 'D': return 'bg-orange-500 text-white';
    case 'F': return 'bg-red-500 text-white';
    default: return 'bg-gray-500 text-white';
  }
});

const coreWebVitals = computed(() => [
  {
    name: 'Largest Contentful Paint',
    value: props.analysis.lcp_score || 0,
    unit: 's',
    description: 'Time until largest element is rendered',
    status: evaluateMetric(props.analysis.lcp_score || 0, CORE_WEB_VITALS_THRESHOLDS.lcp)
  },
  {
    name: 'First Input Delay',
    value: props.analysis.fid_score || 0,
    unit: 'ms',
    description: 'Time to respond to user interaction',
    status: evaluateMetric(props.analysis.fid_score || 0, CORE_WEB_VITALS_THRESHOLDS.fid)
  },
  {
    name: 'Cumulative Layout Shift',
    value: props.analysis.cls_score || 0,
    unit: '',
    description: 'Visual stability during page load',
    status: evaluateMetric(props.analysis.cls_score || 0, CORE_WEB_VITALS_THRESHOLDS.cls)
  }
]);

const getStatusColor = (status: string) => {
  switch (status) {
    case 'good': return 'text-green-600';
    case 'needs-improvement': return 'text-yellow-600';
    case 'poor': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

const formatValue = (value: number, unit: string) => {
  if (unit === 's') return `${value.toFixed(2)}s`;
  if (unit === 'ms') return `${Math.round(value)}ms`;
  return value.toFixed(3);
};

const toggleAdvancedMetrics = () => {
  showAdvancedMetrics.value = !showAdvancedMetrics.value;
};

const generateAdvancedSummary = async () => {
  if (isLoadingAdvancedSummary.value) return;

  isLoadingAdvancedSummary.value = true;
  advancedSummaryError.value = null;

  try {
    const response = await fetch('/api/generate-advanced-metrics-summary', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ analysisId: props.analysis.id })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to generate advanced summary');
    }

    advancedSummary.value = data.summary;
  } catch (error) {
    console.error('Error generating advanced summary:', error);
    advancedSummaryError.value = error instanceof Error ? error.message : 'Failed to generate summary';
  } finally {
    isLoadingAdvancedSummary.value = false;
  }
};

const toggleTooltip = (tooltipId: string) => {
  showTooltip.value = showTooltip.value === tooltipId ? null : tooltipId;
};

const getTooltipContent = (metric: string) => {
  const tooltips = {
    lcp: 'Largest Contentful Paint measures how long it takes for the main content to load. Good: ≤2.5s, Needs Improvement: ≤4s, Poor: >4s',
    fid: 'First Input Delay measures how long it takes for the page to respond to user interactions. Good: ≤100ms, Needs Improvement: ≤300ms, Poor: >300ms',
    cls: 'Cumulative Layout Shift measures visual stability - how much content moves around. Good: ≤0.1, Needs Improvement: ≤0.25, Poor: >0.25',
    performance: 'Overall performance score based on multiple factors including loading speed, interactivity, and visual stability. Scored 0-100.'
  };
  return tooltips[metric as keyof typeof tooltips] || '';
};

const getPriorityColor = (priority: string) => {
  switch (priority.toLowerCase()) {
    case 'high': return 'bg-red-100 text-red-800';
    case 'medium': return 'bg-yellow-100 text-yellow-800';
    case 'low': return 'bg-green-100 text-green-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const lighthouseMetrics = computed(() => {
  if (!props.analysis.lighthouse_data) return null;

  try {
    const data = typeof props.analysis.lighthouse_data === 'string'
      ? JSON.parse(props.analysis.lighthouse_data)
      : props.analysis.lighthouse_data;

    // Handle the structure from analyze-performance.ts
    if (data.performanceScore !== undefined) {
      return {
        performanceScore: data.performanceScore,
        accessibilityScore: data.accessibilityScore,
        bestPracticesScore: data.bestPracticesScore,
        seoScore: data.seoScore
      };
    }

    // Handle the structure from comprehensive-analysis.ts (legacy)
    if (data.categories) {
      return {
        performanceScore: Math.round((data.categories.performance?.score || 0) * 100),
        accessibilityScore: Math.round((data.categories.accessibility?.score || 0) * 100),
        bestPracticesScore: Math.round((data.categories['best-practices']?.score || 0) * 100),
        seoScore: Math.round((data.categories.seo?.score || 0) * 100)
      };
    }

    return null;
  } catch (error) {
    console.error('Error parsing lighthouse data:', error);
    return null;
  }
});
</script>

<template>
  <div class="space-y-6">
    <!-- AI Performance Impact Analysis -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <TrendingUp class="w-5 h-5 text-gray-400 mr-2" />
          <h3 class="text-lg font-semibold text-gray-900">AI Performance Impact Analysis</h3>
          

        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoadingAI" class="text-center py-12">
        <Loader2 class="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
        <p class="text-gray-600">Analyzing performance data to generate contextual insights...</p>
      </div>

      <!-- AI Performance Insights -->
      <div v-else-if="aiPerformanceInsights">
        <!-- Primary Issue Analysis -->
        <div class="bg-red-50 rounded-lg p-4 border border-red-200 mb-6">
          <h4 class="font-medium text-red-900 mb-3 flex items-center">
            <AlertTriangle class="w-4 h-4 mr-2" />
            {{ aiPerformanceInsights.primaryIssue.title }}
          </h4>
          <p class="text-red-800 text-sm mb-3">{{ aiPerformanceInsights.primaryIssue.description }}</p>
          <div class="space-y-2 text-xs">
            <p class="text-red-700"><strong>Evidence:</strong> {{ aiPerformanceInsights.primaryIssue.evidence }}</p>
            <p class="text-red-700"><strong>Conversion Impact:</strong> {{ aiPerformanceInsights.primaryIssue.conversionImpact }}</p>
          </div>
        </div>

        <!-- Performance Overview -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <!-- Overall Performance Score -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-500 mb-1">Overall Performance Score</p>
                <div class="flex items-center space-x-3">
                  <span class="text-3xl font-bold text-orange-500">
                    {{ analysis.performance_score || 0 }}
                  </span>
                  <span class="text-gray-500">/100</span>
                  <span 
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                    :class="performanceGradeColor"
                  >
                    Grade {{ analysis.performance_grade || 'N/A' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- AI Business Impact -->
            <div class="bg-blue-50 rounded-lg p-4">
              <div class="flex items-start">
                <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                <div>
                  <h4 class="font-medium text-blue-900 mb-1">AI Business Impact</h4>
                  <p class="text-sm text-blue-700">{{ aiPerformanceInsights.businessImpact }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Core Web Vitals -->
          <div class="space-y-4">
            <div
              v-for="vital in coreWebVitals"
              :key="vital.name"
              class="relative flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200"
            >
              <div class="flex-1">
                <div class="flex items-center justify-between mb-1">
                  <div class="flex items-center">
                    <h5 class="font-medium text-gray-900 text-sm">{{ vital.name }}</h5>
                    <button
                      @click="toggleTooltip(vital.name.toLowerCase().replace(/\s+/g, ''))"
                      class="ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                    >
                      <HelpCircle class="w-3 h-3" />
                    </button>
                  </div>
                  <span
                    class="text-lg font-bold"
                    :class="getStatusColor(vital.status)"
                  >
                    {{ formatValue(vital.value, vital.unit) }}
                  </span>
                </div>
                <p class="text-xs text-gray-500">{{ vital.description }}</p>

                <!-- Tooltip -->
                <div
                  v-if="showTooltip === vital.name.toLowerCase().replace(/\s+/g, '')"
                  class="absolute z-30 bg-white border border-gray-200 rounded-lg shadow-lg p-3 text-xs max-w-xs left-0 right-0 text-gray-700"
                  style="bottom: 100%; margin-bottom: 8px;"
                >
                  {{ getTooltipContent(vital.name.toLowerCase().replace(/\s+/g, '').substring(0, 3)) }}
                  <!-- Arrow pointing down -->
                  <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Key Findings -->
        <div v-if="aiPerformanceInsights.keyFindings.length > 0" class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h4 class="font-medium text-gray-900 mb-4 flex items-center">
            <Info class="w-4 h-4 mr-2" />
            AI-Identified Key Findings
          </h4>
          <div class="space-y-3">
            <div 
              v-for="(finding, index) in aiPerformanceInsights.keyFindings" 
              :key="index"
              class="p-3 bg-blue-50 rounded-lg border border-blue-200"
            >
              <div class="flex items-center justify-between mb-2">
                <h5 class="font-medium text-blue-900 text-sm">{{ finding.metric }}</h5>
                <div class="text-xs text-blue-700">
                  {{ finding.currentValue }} → {{ finding.targetValue }}
                </div>
              </div>
              <p class="text-sm text-blue-800">{{ finding.impact }}</p>
            </div>
          </div>
        </div>

        <!-- AI Recommendations -->
        <div v-if="aiPerformanceInsights.recommendations.length > 0" class="bg-white rounded-lg border border-gray-200 p-6">
          <div class="flex items-center mb-4">
            <CheckCircle class="w-5 h-5 text-gray-400 mr-2" />
            <h3 class="text-lg font-semibold text-gray-900">AI Performance Recommendations</h3>
          </div>

          <div class="space-y-4">
            <div
              v-for="(recommendation, index) in aiPerformanceInsights.recommendations"
              :key="index"
              class="border border-gray-200 rounded-lg p-4"
            >
              <div class="flex items-start justify-between mb-3">
                <h4 class="font-medium text-gray-900">{{ recommendation.title }}</h4>
                <span 
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  :class="getPriorityColor(recommendation.priority)"
                >
                  {{ recommendation.priority }} Priority
                </span>
              </div>
              <p class="text-gray-700 text-sm mb-3">{{ recommendation.description }}</p>
              <div class="bg-green-50 rounded p-3 border border-green-200">
                <p class="text-green-800 text-sm">
                  <strong>Expected Improvement:</strong> {{ recommendation.expectedImprovement }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="aiError" class="text-center py-12">
        <AlertTriangle class="w-8 h-8 text-yellow-500 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">AI Analysis Unavailable</h3>
        <p class="text-gray-600 mb-4">{{ aiError }}</p>
        <button 
          @click="loadAIPerformanceInsights" 
          class="btn-primary"
        >
          Retry AI Analysis
        </button>
      </div>
    </div>

    <!-- Advanced Metrics Toggle -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <button
        @click="toggleAdvancedMetrics"
        class="flex items-center justify-between w-full text-left"
      >
        <span class="font-medium text-gray-900">Advanced Metrics</span>
        <svg 
          class="w-5 h-5 text-gray-400 transition-transform duration-300"
          :class="{ 'rotate-180': showAdvancedMetrics }"
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <!-- Advanced Metrics Content -->
      <div 
        v-if="showAdvancedMetrics"
        class="mt-4 p-4 bg-gray-50 rounded-lg transition-all duration-300 ease-in-out"
      >
        <div v-if="lighthouseMetrics" class="space-y-3">
          <div class="flex items-center mb-3">
            <h4 class="font-medium text-gray-900">Full Lighthouse Report</h4>
            <button
              @click="toggleTooltip('lighthouse-report')"
              class="ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
            >
              <Info class="w-4 h-4" />
            </button>
          </div>

          <!-- Tooltip for Lighthouse Report -->
          <div
            v-if="showTooltip === 'lighthouse-report'"
            class="absolute z-30 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm text-gray-700"
            style="bottom: 100%; margin-bottom: 8px; left: 0;"
          >
            <p class="text-sm">
              Lighthouse analyzes your website across four key areas: Performance (loading speed),
              Accessibility (usability for all users), Best Practices (modern web standards),
              and SEO (search engine optimization).
            </p>
            <!-- Arrow pointing down -->
            <div class="absolute top-full left-6 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"></div>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div class="text-center p-3 bg-white rounded border relative">
              <div class="flex items-center justify-center mb-1">
                <span class="font-semibold text-gray-900">Performance</span>
                <button
                  @click="toggleTooltip('performance-metric')"
                  class="ml-1 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                >
                  <Info class="w-3 h-3" />
                </button>
              </div>
              <div class="text-lg font-bold text-orange-500">
                {{ lighthouseMetrics.performanceScore || analysis.performance_score || 0 }}
              </div>

              <!-- Performance Tooltip -->
              <div
                v-if="showTooltip === 'performance-metric'"
                class="absolute z-30 bg-white border border-gray-200 rounded-lg shadow-lg p-3 max-w-xs left-1/2 transform -translate-x-1/2 text-gray-700"
                style="bottom: 100%; margin-bottom: 8px;"
              >
                <p class="text-xs">
                  Measures loading performance including First Contentful Paint, Largest Contentful Paint,
                  and other speed metrics. Higher scores indicate faster loading times.
                </p>
                <!-- Arrow pointing down -->
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"></div>
              </div>
            </div>

            <div class="text-center p-3 bg-white rounded border relative">
              <div class="flex items-center justify-center mb-1">
                <span class="font-semibold text-gray-900">Accessibility</span>
                <button
                  @click="toggleTooltip('accessibility-metric')"
                  class="ml-1 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                >
                  <Info class="w-3 h-3" />
                </button>
              </div>
              <div class="text-lg font-bold text-green-500">
                {{ lighthouseMetrics.accessibilityScore || 0 }}
              </div>

              <!-- Accessibility Tooltip -->
              <div
                v-if="showTooltip === 'accessibility-metric'"
                class="absolute z-30 bg-white border border-gray-200 rounded-lg shadow-lg p-3 max-w-xs left-1/2 transform -translate-x-1/2 text-gray-700"
                style="bottom: 100%; margin-bottom: 8px;"
              >
                <p class="text-xs">
                  Evaluates how accessible your website is to users with disabilities.
                  Includes checks for alt text, color contrast, keyboard navigation, and screen reader compatibility.
                </p>
                <!-- Arrow pointing down -->
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"></div>
              </div>
            </div>

            <div class="text-center p-3 bg-white rounded border relative">
              <div class="flex items-center justify-center mb-1">
                <span class="font-semibold text-gray-900">Best Practices</span>
                <button
                  @click="toggleTooltip('best-practices-metric')"
                  class="ml-1 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                >
                  <Info class="w-3 h-3" />
                </button>
              </div>
              <div class="text-lg font-bold text-blue-500">
                {{ lighthouseMetrics.bestPracticesScore || 0 }}
              </div>

              <!-- Best Practices Tooltip -->
              <div
                v-if="showTooltip === 'best-practices-metric'"
                class="absolute z-30 bg-white border border-gray-200 rounded-lg shadow-lg p-3 max-w-xs left-1/2 transform -translate-x-1/2 text-gray-700"
                style="bottom: 100%; margin-bottom: 8px;"
              >
                <p class="text-xs">
                  Checks for modern web development best practices including HTTPS usage,
                  console errors, deprecated APIs, and security vulnerabilities.
                </p>
                <!-- Arrow pointing down -->
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"></div>
              </div>
            </div>

            <div class="text-center p-3 bg-white rounded border relative">
              <div class="flex items-center justify-center mb-1">
                <span class="font-semibold text-gray-900">SEO</span>
                <button
                  @click="toggleTooltip('seo-metric')"
                  class="ml-1 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                >
                  <Info class="w-3 h-3" />
                </button>
              </div>
              <div class="text-lg font-bold text-purple-500">
                {{ lighthouseMetrics.seoScore || analysis.seo_score || 0 }}
              </div>

              <!-- SEO Tooltip -->
              <div
                v-if="showTooltip === 'seo-metric'"
                class="absolute z-30 bg-white border border-gray-200 rounded-lg shadow-lg p-3 max-w-xs left-1/2 transform -translate-x-1/2 text-gray-700"
                style="bottom: 100%; margin-bottom: 8px;"
              >
                <p class="text-xs">
                  Evaluates basic SEO factors including meta descriptions, title tags,
                  crawlability, and mobile-friendliness that affect search engine rankings.
                </p>
                <!-- Arrow pointing down -->
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"></div>
              </div>
            </div>
          </div>

          <!-- Advanced Summary Generation -->
          <div class="mt-6 pt-4 border-t border-gray-200">
            <div class="flex items-center justify-between mb-3">
              <h5 class="font-medium text-gray-900">AI-Powered Advanced Analysis</h5>
              <button
                @click="generateAdvancedSummary"
                :disabled="isLoadingAdvancedSummary"
                class="btn-secondary text-sm"
              >
                <TrendingUp class="w-4 h-4 mr-1" />
                {{ isLoadingAdvancedSummary ? 'Generating...' : 'Generate Detailed Analysis' }}
              </button>
            </div>

            <!-- Advanced Summary Content -->
            <div v-if="advancedSummary" class="space-y-4 text-sm">
              <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <h6 class="font-medium text-blue-900 mb-2">Performance Breakdown</h6>
                <div v-if="advancedSummary.performance_breakdown" class="space-y-2 text-blue-800">
                  <p v-if="advancedSummary.performance_breakdown.coreWebVitals">
                    <strong>Core Web Vitals:</strong> {{ advancedSummary.performance_breakdown.coreWebVitals }}
                  </p>
                  <p v-if="advancedSummary.performance_breakdown.loadingMetrics">
                    <strong>Loading:</strong> {{ advancedSummary.performance_breakdown.loadingMetrics }}
                  </p>
                </div>
              </div>

              <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                <h6 class="font-medium text-green-900 mb-2">Business Impact</h6>
                <p class="text-green-800">{{ advancedSummary.business_impact_assessment }}</p>
              </div>

              <div class="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                <h6 class="font-medium text-yellow-900 mb-2">Optimization Priorities</h6>
                <ul class="text-yellow-800 space-y-1">
                  <li v-for="(priority, index) in advancedSummary.optimization_priorities" :key="index" class="flex items-start">
                    <span class="font-medium mr-2">{{ index + 1 }}.</span>
                    <span>{{ priority }}</span>
                  </li>
                </ul>
              </div>
            </div>

            <!-- Error State -->
            <div v-else-if="advancedSummaryError" class="text-center py-4">
              <p class="text-red-600 text-sm">{{ advancedSummaryError }}</p>
              <button @click="generateAdvancedSummary" class="btn-secondary text-sm mt-2">
                Try Again
              </button>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500">
          <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p>Advanced metrics not available</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}

.btn-secondary {
  @apply inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}

.btn-secondary:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.rotate-180 {
  transform: rotate(180deg);
}
</style>