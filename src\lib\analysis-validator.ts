import { z } from 'zod';

// Define the base schema for an analysis record
const baseAnalysesSchema = z.object({
  user_id: z.string().uuid({ message: "Invalid user_id format (must be UUID)" }),
  url: z.string().url({ message: "Invalid URL format" }).min(1, { message: "URL cannot be empty" }),
  title: z.string().min(1, { message: "Title cannot be empty" }),
  score: z.number({ invalid_type_error: "Score must be a number" }),
  conversion_rate: z.number({ invalid_type_error: "Conversion rate must be a number" }),
  pros: z.array(z.string(), { invalid_type_error: "Pros must be an array of strings" }),
  cons: z.array(z.string(), { invalid_type_error: "Cons must be an array of strings" }),
  recommendations: z.array(z.string(), { invalid_type_error: "Recommendations must be an array of strings" }),
  target_audience: z.string().min(1, { message: "Target audience cannot be empty" }),
  adaptations: z.array(z.string(), { invalid_type_error: "Adaptations must be an array of strings" }),
  
  // Optional fields with default values or nullable
  id: z.string().uuid({ message: "Invalid id format (must be UUID)" }).optional(), // DEFAULT gen_random_uuid()
  created_at: z.string().datetime({ message: "Invalid created_at format (must be datetime)" }).optional(), // DEFAULT now()
  updated_at: z.string().datetime({ message: "Invalid updated_at format (must be datetime)" }).optional(), // DEFAULT now()
  screenshot_url: z.string().url({ message: "Invalid screenshot_url format" }).nullable().optional(),
  screenshot_date: z.string().datetime({ message: "Invalid screenshot_date format" }).nullable().optional(),
  priority_issues_count: z.number().int({ message: "Priority issues count must be an integer" }).min(0, { message: "Priority issues count cannot be negative" }).default(0).optional(),
  suggestions_count: z.number().int({ message: "Suggestions count must be an integer" }).min(0, { message: "Suggestions count cannot be negative" }).default(0).optional(),
  performance_score: z.number().int({ message: "Performance score must be an integer" }).min(0, { message: "Performance score must be between 0 and 100" }).max(100, { message: "Performance score must be between 0 and 100" }).nullable().optional(),
  performance_grade: z.enum(['A', 'B', 'C', 'D', 'F'], { message: "Invalid performance_grade. Must be A, B, C, D, or F" }).nullable().optional(),
  lcp_score: z.number({ invalid_type_error: "LCP score must be a number" }).nullable().optional(),
  fid_score: z.number({ invalid_type_error: "FID score must be a number" }).nullable().optional(),
  cls_score: z.number({ invalid_type_error: "CLS score must be a number" }).nullable().optional(),
  seo_score: z.number().int({ message: "SEO score must be an integer" }).min(0, { message: "SEO score must be between 0 and 100" }).max(100, { message: "SEO score must be between 0 and 100" }).nullable().optional(),
  lighthouse_data: z.any().nullable().optional(), // JSONB can be any valid JSON
  seo_data: z.any().nullable().optional(), // JSONB can be any valid JSON
});

// Schema for inserting a new analysis record (all non-default fields are required)
export const AnalysesInsertSchema = baseAnalysesSchema;

// Schema for updating an analysis record (all fields are optional)
export const AnalysesUpdateSchema = baseAnalysesSchema.partial();

// Type for the validated payload
export type AnalysesInsertPayload = z.infer<typeof AnalysesInsertSchema>;
export type AnalysesUpdatePayload = z.infer<typeof AnalysesUpdateSchema>;

/**
 * Validates a JSON payload for inserting or updating an 'analyses' record.
 *
 * @param payload The JSON object to validate.
 * @param isUpdate If true, validates against the update schema (all fields optional).
 *                 If false, validates against the insert schema (required fields enforced).
 * @returns An object with `success` (boolean) and either `data` (validated payload)
 *          or `errors` (an object mapping field names to error messages).
 */
export function validateAnalysesPayload(payload: any, isUpdate: boolean = false) {
  const schema = isUpdate ? AnalysesUpdateSchema : AnalysesInsertSchema;
  const result = schema.safeParse(payload);

  if (!result.success) {
    const errors: { [key: string]: string } = {};
    // Zod's error.format() provides a structured error object
    const formattedErrors = result.error.format();

    // Iterate over the keys of the original payload to check for errors
    for (const key of Object.keys(payload)) {
      const fieldErrors = formattedErrors[key as keyof typeof formattedErrors];
      if (fieldErrors && '_errors' in fieldErrors && fieldErrors._errors.length > 0) {
        errors[key] = fieldErrors._errors.join(', ');
      }
    }
    return { success: false, errors };
  }

  return { success: true, data: result.data };
}
