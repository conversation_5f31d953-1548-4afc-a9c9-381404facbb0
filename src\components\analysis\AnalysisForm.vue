<script setup lang="ts">
import { ref, computed } from 'vue';
import { supabase } from '../../lib/supabase';
import { fetchLandingPageContent } from '../../lib/url-parser';
import { generateComprehensiveAnalysis } from '../../lib/ai-analyzer';
import { validateUrl, normalizeUrl } from '../../lib/webscraper';
import { Search, Loader2, AlertCircle, CheckCircle } from 'lucide-vue-next';

const url = ref('');
const loading = ref(false);
const error = ref('');
const success = ref('');

const isValidUrl = computed(() => {
  if (!url.value.trim()) return false;
  const validation = validateUrl(normalizeUrl(url.value));
  return validation.isValid;
});

const analyzeWebsite = async () => {
  const normalizedUrl = normalizeUrl(url.value);
  const validation = validateUrl(normalizedUrl);
  
  if (!validation.isValid) {
    error.value = validation.error || 'Invalid URL';
    return;
  }
  
  try {
    loading.value = true;
    error.value = '';
    success.value = '';
    
    // Check if user is authenticated
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) {
      error.value = 'Please sign in to analyze websites';
      return;
    }
    
    // Step 1: Create initial analysis record
    const { data: analysisData, error: insertError } = await supabase
      .from('analyses')
      .insert({
        user_id: userData.user.id,
        url: normalizedUrl,
        title: new URL(normalizedUrl).hostname,
        score: 0, // Will be updated after analysis
        conversion_rate: 0, // Will be updated after analysis
        pros: [],
        cons: [],
        recommendations: [],
        target_audience: 'General web users',
        adaptations: []
      })
      .select('id')
      .single();
    
    if (insertError) {
      throw new Error(`Failed to create analysis record: ${insertError.message}`);
    }
    
    const analysisId = analysisData.id;
    
    // Step 2: Fetch website content
    const html = await fetchLandingPageContent(normalizedUrl);
    
    // Step 3: Run comprehensive analysis
    const response = await fetch('/api/comprehensive-analysis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: normalizedUrl,
        analysisId: analysisId
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Analysis failed');
    }
    
    const analysisResult = await response.json();
    
    if (!analysisResult.success) {
      throw new Error('Analysis completed but with errors');
    }
    
    success.value = 'Analysis completed successfully!';
    
    // Redirect to analysis results page
    setTimeout(() => {
      window.location.href = `/dashboard/analysis/${analysisId}`;
    }, 1500);
    
  } catch (e) {
    console.error('Error analyzing website:', e);
    error.value = e instanceof Error ? e.message : 'An error occurred while analyzing the website';
  } finally {
    loading.value = false;
  }
};

const clearMessages = () => {
  error.value = '';
  success.value = '';
};
</script>

<template>
  <div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
      <!-- Header -->
      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-6 border-b border-gray-200">
        <div class="text-center">
          <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Search class="w-8 h-8 text-white" />
          </div>
          <h1 class="text-2xl font-bold text-gray-900 mb-2">Analyze Your Landing Page</h1>
          <p class="text-gray-600 max-w-md mx-auto">
            Get AI-powered insights, performance metrics, and actionable recommendations to boost your conversion rates
          </p>
        </div>
      </div>

      <!-- Form Content -->
      <div class="p-8">
        <div class="space-y-6">
          <!-- URL Input -->
          <div>
            <label for="url" class="block text-sm font-medium text-gray-700 mb-2">
              Website URL
            </label>
            <div class="relative">
              <input
                id="url"
                v-model="url"
                @input="clearMessages"
                type="text"
                placeholder="https://example.com or example.com"
                class="input-field-lg pr-12"
                :disabled="loading"
                @keyup.enter="analyzeWebsite"
              >
              <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                <CheckCircle v-if="url && isValidUrl" class="w-5 h-5 text-green-500" />
                <AlertCircle v-else-if="url && !isValidUrl" class="w-5 h-5 text-red-500" />
              </div>
            </div>
            <p class="mt-2 text-sm text-gray-500">
              Enter any public website URL. We support all formats (example.com, www.example.com, https://example.com)
              )
            </p>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex items-start">
              <AlertCircle class="w-5 h-5 text-red-400 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h3 class="text-red-800 font-medium">Analysis Error</h3>
                <p class="text-red-700 mt-1">{{ error }}</p>
              </div>
            </div>
          </div>

          <!-- Success Message -->
          <div v-if="success" class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex items-start">
              <CheckCircle class="w-5 h-5 text-green-400 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h3 class="text-green-800 font-medium">Analysis Complete</h3>
                <p class="text-green-700 mt-1">{{ success }}</p>
              </div>
            </div>
          </div>

          <!-- Analyze Button -->
          <button
            @click="analyzeWebsite"
            :disabled="loading || !isValidUrl"
            class="w-full btn-primary py-4 text-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="loading" class="flex items-center justify-center">
              <Loader2 class="w-5 h-5 mr-3 animate-spin" />
              Analyzing Website...
            </span>
            <span v-else class="flex items-center justify-center">
              <Search class="w-5 h-5 mr-3" />
              Analyze Landing Page
            </span>
          </button>

          <!-- Loading Progress -->
          <div v-if="loading" class="space-y-4">
            <div class="bg-blue-50 rounded-lg p-4">
              <h4 class="font-medium text-blue-900 mb-3">Analysis in Progress</h4>
              <div class="space-y-3">
                <div class="flex items-center text-sm text-blue-800">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mr-3 animate-pulse"></div>
                  Fetching website content...
                </div>
                <div class="flex items-center text-sm text-blue-800">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mr-3 animate-pulse" style="animation-delay: 0.5s"></div>
                  Running performance analysis...
                </div>
                <div class="flex items-center text-sm text-blue-800">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mr-3 animate-pulse" style="animation-delay: 1s"></div>
                  Generating AI insights...
                </div>
                <div class="flex items-center text-sm text-blue-800">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mr-3 animate-pulse" style="animation-delay: 1.5s"></div>
                  Saving results...
                </div>
              </div>
            </div>
          </div>

          <!-- How it Works -->
          <div class="bg-gray-50 rounded-lg p-6">
            <h4 class="font-medium text-gray-900 mb-4">What We Analyze</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Search class="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <h5 class="font-medium text-gray-900">SEO Analysis</h5>
                  <p class="text-sm text-gray-600">Meta tags, headings, structured data</p>
                </div>
              </div>
              
              <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h5 class="font-medium text-gray-900">Performance</h5>
                  <p class="text-sm text-gray-600">Core Web Vitals, loading speed</p>
                </div>
              </div>
              
              <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <div>
                  <h5 class="font-medium text-gray-900">AI Insights</h5>
                  <p class="text-sm text-gray-600">Conversion optimization, lead insights</p>
                </div>
              </div>
              
              <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                  </svg>
                </div>
                <div>
                  <h5 class="font-medium text-gray-900">Chat Support</h5>
                  <p class="text-sm text-gray-600">Ask questions about your results</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Example URLs -->
          <div class="text-center">
            <p class="text-sm text-gray-500 mb-3">Try these example URLs:</p>
            <div class="flex flex-wrap justify-center gap-2">
              <button
                v-for="exampleUrl in ['stripe.com', 'github.com', 'supabase.com']"
                :key="exampleUrl"
                @click="url = exampleUrl; clearMessages()"
                class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
                :disabled="loading"
              >
                {{ exampleUrl }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>