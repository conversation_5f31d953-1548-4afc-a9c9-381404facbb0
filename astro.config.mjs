import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import vue from "@astrojs/vue";
import node from "@astrojs/node";

// https://astro.build/config
export default defineConfig({
  integrations: [
    tailwind(),
    vue({
      appEntrypoint: '/src/plugins/motion.ts'
    })
  ],
  output: 'server',
  adapter: node({
    mode: "standalone"
  }),
  server: {
    host: true
  }
});