-- Migration to update SE<PERSON> issues severity from enum to numeric score
-- This script converts the severity field to severity_score (1-5 numeric scale)

-- Step 1: Add the new severity_score column
ALTER TABLE seo_issues ADD COLUMN severity_score INTEGER;

-- Step 2: Migrate existing data
-- Convert existing severity values to numeric scores
UPDATE seo_issues 
SET severity_score = CASE 
  WHEN severity = 'critical' THEN 5
  WHEN severity = 'warning' THEN 3
  WHEN severity = 'info' THEN 1
  ELSE 2
END;

-- Step 3: Make severity_score NOT NULL and add constraints
ALTER TABLE seo_issues 
ALTER COLUMN severity_score SET NOT NULL,
ADD CONSTRAINT severity_score_range CHECK (severity_score >= 1 AND severity_score <= 5);

-- Step 4: Drop the old severity column
ALTER TABLE seo_issues DROP COLUMN severity;

-- Step 5: Update any indexes that referenced the old severity column
DROP INDEX IF EXISTS idx_seo_issues_severity;
CREATE INDEX idx_seo_issues_severity_score ON seo_issues(severity_score DESC);

-- Step 6: Update the analyses table priority_issues_count calculation
-- This will be handled by the application logic, but we can add a function if needed
CREATE OR REPLACE FUNCTION update_priority_issues_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE analyses 
  SET priority_issues_count = (
    SELECT COUNT(*) 
    FROM seo_issues 
    WHERE analysis_id = NEW.analysis_id 
    AND severity_score >= 4
  )
  WHERE id = NEW.analysis_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update priority_issues_count
DROP TRIGGER IF EXISTS trigger_update_priority_issues_count ON seo_issues;
CREATE TRIGGER trigger_update_priority_issues_count
  AFTER INSERT OR UPDATE OR DELETE ON seo_issues
  FOR EACH ROW
  EXECUTE FUNCTION update_priority_issues_count();

-- Verify the migration
SELECT 
  'Migration completed successfully' as status,
  COUNT(*) as total_issues,
  COUNT(*) FILTER (WHERE severity_score >= 4) as high_priority_issues,
  COUNT(*) FILTER (WHERE severity_score = 3) as medium_priority_issues,
  COUNT(*) FILTER (WHERE severity_score <= 2) as low_priority_issues
FROM seo_issues;
