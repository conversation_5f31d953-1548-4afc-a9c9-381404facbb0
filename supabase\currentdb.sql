-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.advanced_metrics_summary (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  lighthouse_summary text,
  performance_breakdown jsonb DEFAULT '{}'::jsonb,
  accessibility_insights text,
  best_practices_analysis text,
  seo_technical_summary text,
  mobile_performance_notes text,
  optimization_priorities jsonb DEFAULT '[]'::jsonb,
  business_impact_assessment text,
  technical_debt_analysis text,
  monitoring_recommendations jsonb DEFAULT '[]'::jsonb,
  generated_at timestamp with time zone DEFAULT now(),
  ai_generated boolean DEFAULT true,
  CONSTRAINT advanced_metrics_summary_pkey PRIMARY KEY (id),
  CONSTRAINT advanced_metrics_summary_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_chat_context (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  website_summary text NOT NULL,
  key_metrics jsonb NOT NULL DEFAULT '{}'::jsonb,
  business_context text NOT NULL,
  suggested_prompts jsonb NOT NULL DEFAULT '[]'::jsonb,
  conversation_starters jsonb NOT NULL DEFAULT '[]'::jsonb,
  technical_context jsonb NOT NULL DEFAULT '{}'::jsonb,
  optimization_opportunities jsonb NOT NULL DEFAULT '[]'::jsonb,
  ai_generated boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_chat_context_pkey PRIMARY KEY (id),
  CONSTRAINT ai_chat_context_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_insights (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  category_id uuid NOT NULL,
  insight_type text CHECK (insight_type = ANY (ARRAY['strength'::text, 'weakness'::text])),
  title text,
  description text,
  evidence text,
  impact_explanation text,
  implementation_steps text,
  business_value text,
  priority_score numeric,
  created_at timestamp with time zone DEFAULT now(),
  display_order integer DEFAULT 0,
  impact_score numeric CHECK (impact_score >= 0::numeric AND impact_score <= 10::numeric),
  effort_required text CHECK (effort_required = ANY (ARRAY['Low'::text, 'Medium'::text, 'High'::text])),
  timeline_estimate text,
  success_metrics text,
  related_metrics jsonb DEFAULT '[]'::jsonb,
  conversion_impact_estimate text,
  technical_requirements text,
  insight_category text DEFAULT 'general'::text,
  confidence_score numeric CHECK (confidence_score >= 0::numeric AND confidence_score <= 1::numeric),
  data_source text DEFAULT 'ai_analysis'::text,
  validation_status text DEFAULT 'pending'::text CHECK (validation_status = ANY (ARRAY['pending'::text, 'validated'::text, 'rejected'::text])),
  user_feedback jsonb DEFAULT '{}'::jsonb,
  implementation_complexity text CHECK (implementation_complexity = ANY (ARRAY['Low'::text, 'Medium'::text, 'High'::text])),
  CONSTRAINT ai_insights_pkey PRIMARY KEY (id),
  CONSTRAINT ai_insights_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id),
  CONSTRAINT ai_insights_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.suggestion_categories(id)
);
CREATE TABLE public.ai_page_summaries (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  page_purpose text,
  key_takeaways jsonb DEFAULT '[]'::jsonb,
  hero_effectiveness text,
  value_proposition_clarity text,
  lead_capture_assessment text,
  user_journey_analysis text,
  business_impact_summary text,
  conversion_assessment text,
  trust_signals_analysis text,
  mobile_experience_notes text,
  competitive_advantages jsonb DEFAULT '[]'::jsonb,
  improvement_priorities jsonb DEFAULT '[]'::jsonb,
  business_model_insights text,
  target_audience_analysis text,
  content_strategy_notes text,
  ai_generated boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_page_summaries_pkey PRIMARY KEY (id),
  CONSTRAINT ai_page_summaries_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.ai_seo_insights (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL UNIQUE,
  meta_optimization_notes text,
  content_seo_assessment text,
  technical_seo_issues jsonb DEFAULT '[]'::jsonb,
  keyword_opportunities jsonb DEFAULT '[]'::jsonb,
  local_seo_recommendations text,
  schema_markup_suggestions jsonb DEFAULT '[]'::jsonb,
  internal_linking_analysis text,
  page_speed_seo_impact text,
  mobile_seo_assessment text,
  competitor_analysis_notes text,
  content_gap_analysis jsonb DEFAULT '[]'::jsonb,
  seo_priority_actions jsonb DEFAULT '[]'::jsonb,
  expected_traffic_impact text,
  ai_generated boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_seo_insights_pkey PRIMARY KEY (id),
  CONSTRAINT ai_seo_insights_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.analyses (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  url text NOT NULL,
  title text NOT NULL,
  score numeric NOT NULL CHECK (score >= 0::numeric AND score <= 10::numeric),
  conversion_rate numeric NOT NULL,
  pros jsonb NOT NULL,
  cons jsonb NOT NULL,
  recommendations jsonb NOT NULL,
  target_audience text NOT NULL,
  adaptations jsonb NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  screenshot_url text,
  screenshot_date timestamp with time zone,
  priority_issues_count integer DEFAULT 0,
  suggestions_count integer DEFAULT 0,
  performance_score integer CHECK (performance_score >= 0 AND performance_score <= 100),
  performance_grade text CHECK (performance_grade = ANY (ARRAY['A'::text, 'B'::text, 'C'::text, 'D'::text, 'F'::text])),
  lcp_score numeric,
  fid_score numeric,
  cls_score numeric,
  seo_score integer CHECK (seo_score >= 0 AND seo_score <= 100),
  lighthouse_data jsonb,
  seo_data jsonb,
  conversion_score_display numeric DEFAULT score,
  overall_rating numeric,
  generation_status text DEFAULT 'pending'::text CHECK (generation_status = ANY (ARRAY['pending'::text, 'in_progress'::text, 'completed'::text, 'failed'::text])),
  generation_started_at timestamp with time zone,
  generation_completed_at timestamp with time zone,
  current_generation_step text,
  total_generation_steps integer DEFAULT 8,
  completed_generation_steps integer DEFAULT 0,
  generation_error text,
  all_content_generated boolean DEFAULT false,
  pros_cons_generated boolean DEFAULT false,
  lead_insights_generated boolean DEFAULT false,
  performance_insights_generated boolean DEFAULT false,
  seo_insights_generated boolean DEFAULT false,
  chat_context_generated boolean DEFAULT false,
  page_summary_enhanced boolean DEFAULT false,
  overall_grade text CHECK (overall_grade = ANY (ARRAY['A'::text, 'B'::text, 'C'::text, 'D'::text, 'F'::text])),
  overall_score integer CHECK (overall_score >= 0 AND overall_score <= 100),
  conversion_rate_score integer CHECK (conversion_rate_score >= 0 AND conversion_rate_score <= 10),
  user_experience_score integer CHECK (user_experience_score >= 0 AND user_experience_score <= 100),
  mobile_score integer CHECK (mobile_score >= 0 AND mobile_score <= 100),
  accessibility_score integer CHECK (accessibility_score >= 0 AND accessibility_score <= 100),
  best_practices_score integer CHECK (best_practices_score >= 0 AND best_practices_score <= 100),
  lighthouse_data_updated_at timestamp with time zone,
  scores_calculated_at timestamp with time zone,
  CONSTRAINT analyses_pkey PRIMARY KEY (id),
  CONSTRAINT analyses_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id)
);
CREATE TABLE public.analysis_generation_progress (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  current_step text NOT NULL,
  step_number integer NOT NULL CHECK (step_number >= 1 AND step_number <= 10),
  total_steps integer NOT NULL DEFAULT 8,
  step_description text NOT NULL,
  status text NOT NULL CHECK (status = ANY (ARRAY['pending'::text, 'in_progress'::text, 'completed'::text, 'failed'::text])),
  started_at timestamp with time zone,
  completed_at timestamp with time zone,
  error_message text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT analysis_generation_progress_pkey PRIMARY KEY (id),
  CONSTRAINT analysis_generation_progress_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.lead_qualification_insights (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid,
  question_type text,
  question_text text,
  context_explanation text,
  qualification_value text,
  suggested_response text,
  priority_level integer,
  business_impact_score numeric CHECK (business_impact_score >= 0::numeric AND business_impact_score <= 10::numeric),
  conversion_relevance text,
  follow_up_questions jsonb DEFAULT '[]'::jsonb,
  objection_handling text,
  qualification_criteria text,
  lead_scoring_weight numeric DEFAULT 1.0,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT lead_qualification_insights_pkey PRIMARY KEY (id),
  CONSTRAINT lead_qualification_insights_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.messages (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  user_id uuid NOT NULL,
  content text NOT NULL,
  role text NOT NULL CHECK (role = ANY (ARRAY['user'::text, 'assistant'::text])),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT messages_pkey PRIMARY KEY (id),
  CONSTRAINT messages_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id),
  CONSTRAINT messages_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.performance_impact_insights (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  impact_type text NOT NULL CHECK (impact_type = ANY (ARRAY['critical'::text, 'opportunity'::text, 'optimization'::text])),
  metric_name text NOT NULL,
  current_value numeric,
  target_value numeric,
  impact_description text NOT NULL,
  conversion_impact text,
  implementation_guide text,
  expected_improvement text,
  effort_level text CHECK (effort_level = ANY (ARRAY['Low'::text, 'Medium'::text, 'High'::text])),
  timeline text,
  core_web_vitals_impact text,
  mobile_performance_notes text,
  lighthouse_recommendations jsonb DEFAULT '[]'::jsonb,
  conversion_correlation text,
  technical_debt_assessment text,
  performance_budget_notes text,
  monitoring_recommendations text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT performance_impact_insights_pkey PRIMARY KEY (id),
  CONSTRAINT performance_impact_insights_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.performance_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  metric_name text NOT NULL,
  metric_value numeric NOT NULL,
  metric_unit text,
  metric_category text,
  is_good boolean,
  threshold_good numeric,
  threshold_poor numeric,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT performance_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT performance_metrics_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.profiles (
  id uuid NOT NULL,
  email text NOT NULL,
  display_name text,
  avatar_url text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT profiles_pkey PRIMARY KEY (id),
  CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id)
);
CREATE TABLE public.seo_issues (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  issue text NOT NULL,
  recommendation text NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  severity_score integer NOT NULL CHECK (severity_score >= 1 AND severity_score <= 5),
  issue_type text DEFAULT 'general'::text,
  fix_instructions text,
  impact_description text,
  CONSTRAINT seo_issues_pkey PRIMARY KEY (id),
  CONSTRAINT seo_issues_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);
CREATE TABLE public.suggestion_categories (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  icon text,
  color text,
  description text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT suggestion_categories_pkey PRIMARY KEY (id)
);
CREATE TABLE public.suggestions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL,
  category text NOT NULL,
  title text NOT NULL,
  description text NOT NULL,
  detailed_explanation text,
  impact_level text NOT NULL CHECK (impact_level = ANY (ARRAY['High'::text, 'Medium'::text, 'Low'::text, 'high'::text, 'medium'::text, 'low'::text, 'HIGH'::text, 'MEDIUM'::text, 'LOW'::text, 'Critical'::text, 'Important'::text, 'Minor'::text, 'critical'::text, 'important'::text, 'minor'::text])),
  effort_level text NOT NULL CHECK (effort_level = ANY (ARRAY['High'::text, 'Medium'::text, 'Low'::text, 'high'::text, 'medium'::text, 'low'::text, 'HIGH'::text, 'MEDIUM'::text, 'LOW'::text, 'Complex'::text, 'Moderate'::text, 'Simple'::text, 'complex'::text, 'moderate'::text, 'simple'::text])),
  priority integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  implementation_steps text,
  expected_impact text,
  business_value text,
  CONSTRAINT suggestions_pkey PRIMARY KEY (id),
  CONSTRAINT suggestions_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.analyses(id)
);