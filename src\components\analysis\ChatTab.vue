<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from "vue";
import {
  Send,
  Sparkles,
  MessageCircle,
  Lightbulb,
  TrendingUp,
  Search,
  BarChart3,
  Code2,
  Mail,
} from "lucide-vue-next";
import { supabase } from "../../lib/supabase";
import type { Database } from "../../types/supabase";
import MarkdownIt from 'markdown-it';
import DOMPurify from 'dompurify';

const props = defineProps<{
  analysis: Database["public"]["Tables"]["analyses"]["Row"];
}>();

interface ChatMessage {
  id: string;
  content: string;
  role: "user" | "assistant";
  timestamp: Date;
}

const messages = ref<ChatMessage[]>([]);
const newMessage = ref("");
const loading = ref(false);
const chatContainer = ref<HTMLElement>();
const loadingHistory = ref(true);
const showAnimations = ref(false);
const initialLoadComplete = ref(false);

// Chat prompt suggestions – loaded from pre-generated context
const chatSuggestions = ref([
  {
    icon: Code2,
    title: "Landing Page Review",
    prompt:
      "Can you analyze my landing page and suggest ways to improve conversions?",
  },
  {
    icon: Lightbulb,
    title: "Conversion Ideas",
    prompt: "Suggest smart changes to boost signups on my homepage.",
  },
  {
    icon: Mail,
    title: "Lead Capture Help",
    prompt: "How can I optimize my contact form to get more qualified leads?",
  },
  {
    icon: Sparkles,
    title: "Performance Boost",
    prompt: "Analyze my site speed and tell me what’s slowing it down.",
  },
]);

const chatContext = ref<{
  websiteSummary: string;
  keyMetrics: any;
  businessContext: string;
  suggestedPrompts: string[];
  conversationStarters: string[];
  technicalContext: any;
  optimizationOpportunities: string[];
} | null>(null);

// Load pre-generated chat context
const loadChatContext = async () => {
  try {
    const { data: contextData, error } = await supabase
      .from('ai_chat_context')
      .select('*')
      .eq('analysis_id', props.analysis.id)
      .single();

    if (contextData && !error) {
      chatContext.value = {
        websiteSummary: contextData.website_summary,
        keyMetrics: contextData.key_metrics,
        businessContext: contextData.business_context,
        suggestedPrompts: contextData.suggested_prompts || [],
        conversationStarters: contextData.conversation_starters || [],
        technicalContext: contextData.technical_context,
        optimizationOpportunities: contextData.optimization_opportunities || []
      };

      // Update chat suggestions with pre-generated prompts
      if (contextData.suggested_prompts && contextData.suggested_prompts.length > 0) {
        chatSuggestions.value = contextData.suggested_prompts.slice(0, 4).map((prompt: string, index: number) => {
          const icons = [Code2, Lightbulb, Mail, Sparkles];
          const titles = ["Website Analysis", "Optimization Ideas", "Lead Generation", "Performance Tips"];
          return {
            icon: icons[index] || Code2,
            title: titles[index] || "AI Suggestion",
            prompt: prompt
          };
        });
      }
    }
  } catch (error) {
    console.error('Error loading chat context:', error);
  }
};

// Get all suggestions to display (matching reference image layout)
const displayedSuggestions = computed(() => {
  return chatSuggestions.value;
});

// Load chat history from database
const loadChatHistory = async () => {
  try {
    loadingHistory.value = true;

    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      console.error("User not authenticated");
      return;
    }

    const { data: chatData, error: chatError } = await supabase
      .from("messages")
      .select("*")
      .eq("analysis_id", props.analysis.id)
      .eq("user_id", userData.user.id)
      .order("created_at", { ascending: true });

    if (chatError) {
      console.error("Error loading chat history:", chatError);
      return;
    }

    if (chatData && chatData.length > 0) {
      messages.value = chatData.map((msg) => ({
        id: msg.id,
        content: msg.content,
        role: msg.role as "user" | "assistant",
        timestamp: new Date(msg.created_at),
      }));
    }
  } catch (error) {
    console.error("Error loading chat history:", error);
  } finally {
    loadingHistory.value = false;
  }
};

// Save message to database
const saveMessage = async (content: string, role: "user" | "assistant") => {
  try {
    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      console.error("User not authenticated");
      return;
    }

    const { error } = await supabase.from("messages").insert({
      analysis_id: props.analysis.id,
      user_id: userData.user.id,
      content: content,
      role: role,
    });

    if (error) {
      console.error("Error saving message:", error);
    }
  } catch (error) {
    console.error("Error saving message:", error);
  }
};

const sendMessage = async (content?: string) => {
  const messageContent = content || newMessage.value.trim();
  if (!messageContent || loading.value) return;

  // Add user message
  const userMessage: ChatMessage = {
    id: Date.now().toString(),
    content: messageContent,
    role: "user",
    timestamp: new Date(),
  };

  messages.value.push(userMessage);
  newMessage.value = "";
  loading.value = true;

  // Save user message to database
  await saveMessage(messageContent, "user");

  // Scroll to bottom
  await nextTick();
  scrollToBottom();

  try {
    // Send to chat API with enhanced context
    const response = await fetch("/api/chat-analysis", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        message: messageContent,
        analysisId: props.analysis.id,
        analysisData: {
          url: props.analysis.url,
          title: props.analysis.title,
          score: props.analysis.score,
          performanceScore: props.analysis.performance_score,
          seoScore: props.analysis.seo_score,
          performanceGrade: props.analysis.performance_grade,
          pros: props.analysis.pros,
          cons: props.analysis.cons,
          recommendations: props.analysis.recommendations,
          targetAudience: props.analysis.target_audience,
          conversionRate: props.analysis.conversion_rate,
          adaptations: props.analysis.adaptations,
          lighthouseData: props.analysis.lighthouse_data,
          createdAt: props.analysis.created_at
        },
        chatContext: chatContext.value ? {
          websiteSummary: chatContext.value.websiteSummary,
          keyMetrics: chatContext.value.keyMetrics,
          businessContext: chatContext.value.businessContext,
          technicalContext: chatContext.value.technicalContext,
          optimizationOpportunities: chatContext.value.optimizationOpportunities
        } : null
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to get response");
    }

    const data = await response.json();

    // Add assistant message
    const assistantMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      content: data.response,
      role: "assistant",
      timestamp: new Date(),
    };

    messages.value.push(assistantMessage);

    // Save assistant message to database
    await saveMessage(data.response, "assistant");

    // Scroll to bottom
    await nextTick();
    scrollToBottom();
  } catch (error) {
    console.error("Error sending message:", error);

    // Add error message
    const errorMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      content: "Sorry, I encountered an error. Please try again.",
      role: "assistant",
      timestamp: new Date(),
    };

    messages.value.push(errorMessage);
  } finally {
    loading.value = false;
  }
};

const scrollToBottom = () => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
  }
};

const useSuggestion = (suggestion: any) => {
  sendMessage(suggestion.prompt);
};

// Simple markdown renderer for basic formatting
const md = new MarkdownIt();

const renderMarkdown = (text: string) => {
  const renderedHtml = md.render(text);
  return DOMPurify.sanitize(renderedHtml);
};

onMounted(async () => {
  // Load pre-generated chat context first
  await loadChatContext();

  // Load existing chat history
  await loadChatHistory();

  // Scroll to bottom after loading
  await nextTick();
  scrollToBottom();

  // Trigger entrance animations
  await nextTick();
  showAnimations.value = true;
  initialLoadComplete.value = true;
});
</script>

<template>
  <div class="flex flex-col h-full min-h-[600px] bg-white">
    <!-- Chat Content Area - Fixed Height to prevent layout shifts -->
    <div class="flex-1 relative" style="min-height: 550px;">
      <!-- Main Content Container (Empty State) -->
      <div
        class="absolute inset-0 flex flex-col items-center justify-center p-8"
        v-if="messages.length === 0 && initialLoadComplete"
      >
      <!-- Sparkle Icon -->
      <div
        class="mb-8 mt-auto"
      >
        <svg
          width="17"
          height="17"
          viewBox="0 0 17 17"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="w-16 h-16 text-gray-700 opacity-40 animate-pulse-slow"
        >
          <mask id="path-1-inside-1_39_53" fill="white">
            <ellipse cx="8.5951" cy="8.40081" rx="0.801083" ry="0.795299" />
          </mask>
          <ellipse
            cx="8.5951"
            cy="8.40081"
            rx="0.801083"
            ry="0.795299"
            fill="currentColor"
          />
          <path
            d="M9.39618 8.40081H8.19618C8.19618 8.16912 8.38299 7.99611 8.5951 7.99611V9.19611V10.3961C9.69205 10.3961 10.5962 9.51097 10.5962 8.40081H9.39618ZM8.5951 9.19611V7.99611C8.8072 7.99611 8.99401 8.16912 8.99401 8.40081H7.79401H6.59401C6.59401 9.51097 7.49814 10.3961 8.5951 10.3961V9.19611ZM7.79401 8.40081H8.99401C8.99401 8.63251 8.8072 8.80551 8.5951 8.80551V7.60551V6.40551C7.49814 6.40551 6.59401 7.29066 6.59401 8.40081H7.79401ZM8.5951 7.60551V8.80551C8.38299 8.80551 8.19618 8.63251 8.19618 8.40081H9.39618H10.5962C10.5962 7.29066 9.69205 6.40551 8.5951 6.40551V7.60551Z"
            fill="currentColor"
            mask="url(#path-1-inside-1_39_53)"
          />
          <path
            d="M8.61401 15.5734C12.5905 15.5734 15.814 12.3498 15.814 8.37339C15.814 4.39694 12.5905 1.17339 8.61401 1.17339C4.63756 1.17339 1.41401 4.39694 1.41401 8.37339C1.41401 12.3498 4.63756 15.5734 8.61401 15.5734Z"
            stroke="currentColor"
            stroke-width="1.2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M8.73901 2.3636C8.82169 2.3772 8.90223 2.40354 8.97631 2.4427L9.08276 2.51106L9.17651 2.59505C9.23396 2.6557 9.28185 2.72537 9.31713 2.8011L9.36108 2.91927L9.36401 2.93001L10.2361 6.31673C10.2631 6.42107 10.3181 6.51687 10.3943 6.59309C10.4705 6.66913 10.5664 6.72342 10.6706 6.75032L14.0574 7.62337L14.0652 7.62532C14.1876 7.65917 14.2998 7.72264 14.3913 7.80891L14.4763 7.90364L14.5447 8.01009C14.6036 8.12133 14.6345 8.24622 14.6345 8.37337C14.6345 8.54312 14.5791 8.70901 14.4763 8.84407C14.3735 8.979 14.2287 9.07624 14.0652 9.12141L14.0574 9.12434L10.6706 9.99641C10.5662 10.0234 10.4706 10.0783 10.3943 10.1546C10.3179 10.231 10.263 10.3265 10.2361 10.431L9.36303 13.8177L9.3601 13.8275C9.31445 13.9904 9.21659 14.1343 9.08178 14.2366C8.947 14.3388 8.78217 14.3938 8.61303 14.3939C8.44391 14.3939 8.2791 14.3387 8.14428 14.2366C8.00946 14.1344 7.91165 13.9904 7.86596 13.8275L7.86303 13.8177L6.98998 10.431C6.96308 10.3267 6.90879 10.2309 6.83276 10.1546C6.75657 10.0784 6.66069 10.0234 6.55639 9.99641L3.16967 9.12337L3.15795 9.12044C2.99564 9.07437 2.85252 8.9757 2.75072 8.84114C2.64898 8.70648 2.59355 8.54214 2.5935 8.37337C2.59355 8.20458 2.64896 8.04026 2.75072 7.90559C2.85252 7.77102 2.99562 7.67335 3.15795 7.62727L3.16967 7.62337L6.55639 6.75032L6.63256 6.72493C6.70703 6.69468 6.77552 6.64932 6.83276 6.59212C6.90888 6.51592 6.963 6.42002 6.98998 6.31575L7.86401 2.93001L7.86596 2.91927C7.9116 2.75648 8.00965 2.61332 8.14428 2.51106C8.27918 2.40878 8.44471 2.35286 8.61401 2.35286L8.73901 2.3636ZM7.95873 6.56575V6.56673C7.88699 6.84403 7.7414 7.09764 7.53881 7.30013C7.33641 7.50222 7.08331 7.64653 6.80639 7.71809L4.26635 8.37337L6.80541 9.02864H6.80639C7.0836 9.10033 7.33731 9.24511 7.53979 9.44759C7.74218 9.65019 7.88719 9.90368 7.95873 10.181L8.61303 12.7191L9.2683 10.181C9.33991 9.90342 9.48455 9.65028 9.68725 9.44759C9.88993 9.24494 10.1431 9.10024 10.4206 9.02864L12.9607 8.37337L10.4206 7.71907C10.1433 7.64753 9.88985 7.50252 9.68725 7.30013C9.48474 7.09762 9.33998 6.84399 9.2683 6.56673V6.56575L8.61303 4.02571L7.95873 6.56575Z"
            fill="currentColor"
          />
          <path
            d="M8.73901 2.3636L8.75529 2.26463L8.74757 2.26397L8.73901 2.3636ZM8.97631 2.4427L9.03054 2.35826L9.02305 2.3543L8.97631 2.4427ZM9.08276 2.51106L9.14948 2.43658C9.14552 2.43303 9.14127 2.4298 9.13679 2.42692L9.08276 2.51106ZM9.17651 2.59505L9.24935 2.52605L9.24323 2.52056L9.17651 2.59505ZM9.31713 2.8011L9.41116 2.76613L9.40778 2.75887L9.31713 2.8011ZM9.36108 2.91927L9.45794 2.89285L9.4548 2.88441L9.36108 2.91927ZM9.36401 2.93001L9.46086 2.90507L9.46048 2.9037L9.36401 2.93001ZM10.2361 6.31673L10.1392 6.34166L10.1393 6.34179L10.2361 6.31673ZM10.3943 6.59309L10.3236 6.6638L10.3237 6.6639L10.3943 6.59309ZM10.6706 6.75032L10.6457 6.84715L10.6457 6.84715L10.6706 6.75032ZM14.0574 7.62337L14.0324 7.7202L14.0331 7.72038L14.0574 7.62337ZM14.0652 7.62532L14.0918 7.52891L14.0894 7.52831L14.0652 7.62532ZM14.3913 7.80891L14.4661 7.74191L14.4599 7.73615L14.3913 7.80891ZM14.4763 7.90364L14.5605 7.8496C14.5576 7.84511 14.5543 7.84085 14.5508 7.83687L14.4763 7.90364ZM14.5447 8.01009L14.6334 7.96313L14.6288 7.95605L14.5447 8.01009ZM14.6345 8.37337L14.7345 8.3734L14.7345 8.37334L14.6345 8.37337ZM14.4763 8.84407L14.5558 8.90468L14.5559 8.90465L14.4763 8.84407ZM14.0652 9.12141L14.0384 9.02464L14.0301 9.02778L14.0652 9.12141ZM14.0574 9.12434L14.0824 9.22174L14.0925 9.21798L14.0574 9.12434ZM10.6706 9.99641L10.6457 9.89957L10.6457 9.89958L10.6706 9.99641ZM10.3943 10.1546L10.3236 10.0839L10.3236 10.0839L10.3943 10.1546ZM10.2361 10.431L10.1392 10.406L10.1392 10.406L10.2361 10.431ZM9.36303 13.8177L9.45889 13.8465L9.45986 13.8427L9.36303 13.8177ZM9.3601 13.8275L9.2643 13.7987L9.26381 13.8005L9.3601 13.8275ZM9.08178 14.2366L9.14219 14.3163L9.14223 14.3163L9.08178 14.2366ZM8.61303 14.3939L8.61303 14.4939L8.61309 14.4939L8.61303 14.3939ZM8.14428 14.2366L8.08384 14.3163L8.08392 14.3164L8.14428 14.2366ZM7.86596 13.8275L7.96226 13.8005L7.96174 13.7987L7.86596 13.8275ZM7.86303 13.8177L7.76612 13.8427L7.76725 13.8464L7.86303 13.8177ZM6.98998 10.431L7.08682 10.406L7.08681 10.406L6.98998 10.431ZM6.83276 10.1546L6.90356 10.084L6.90347 10.0839L6.83276 10.1546ZM6.55639 9.99641L6.58147 9.89961L6.58135 9.89958L6.55639 9.99641ZM3.16967 9.12337L3.19463 9.02653L3.19392 9.02635L3.16967 9.12337ZM3.15795 9.12044L3.13063 9.21669L3.1337 9.21745L3.15795 9.12044ZM2.75072 8.84114L2.67094 8.90142L2.67098 8.90147L2.75072 8.84114ZM2.5935 8.37337L2.4935 8.37334L2.4935 8.3734L2.5935 8.37337ZM2.75072 7.90559L2.67097 7.84527L2.67094 7.8453L2.75072 7.90559ZM3.15795 7.62727L3.18528 7.72357L3.18957 7.72214L3.15795 7.62727ZM3.16967 7.62337L3.14465 7.5263L3.13805 7.5285L3.16967 7.62337ZM6.55639 6.75032L6.58141 6.84739L6.58801 6.84519L6.55639 6.75032ZM6.63256 6.72493L6.66425 6.81999L6.6702 6.81758L6.63256 6.72493ZM6.83276 6.59212L6.90344 6.66285L6.9035 6.66279L6.83276 6.59212ZM6.98998 6.31575L7.08679 6.3408L7.08681 6.34075L6.98998 6.31575ZM7.86401 2.93001L7.96109 2.95507L7.96239 2.9479L7.86401 2.93001ZM7.86596 2.91927L7.76925 2.89215L7.76757 2.90138L7.86596 2.91927ZM8.14428 2.51106L8.08386 2.43138L8.0838 2.43143L8.14428 2.51106ZM8.61401 2.35286L8.6226 2.25286H8.61401V2.35286ZM7.95873 6.56575L7.86189 6.54081C7.85979 6.54895 7.85873 6.55734 7.85873 6.56575H7.95873ZM7.95873 6.56673L8.05555 6.59177C8.05766 6.58359 8.05873 6.57518 8.05873 6.56673H7.95873ZM7.53881 7.30013L7.60947 7.37089L7.6095 7.37085L7.53881 7.30013ZM6.80639 7.71809L6.83137 7.81492L6.83141 7.81491L6.80639 7.71809ZM4.26635 8.37337L4.24137 8.27654C4.19721 8.28793 4.16635 8.32776 4.16635 8.37336C4.16635 8.41897 4.1972 8.4588 4.24136 8.47019L4.26635 8.37337ZM6.80541 9.02864L6.78042 9.12547C6.78859 9.12757 6.79698 9.12864 6.80541 9.12864V9.02864ZM6.80639 9.02864L6.83143 8.93183C6.82325 8.92971 6.81484 8.92864 6.80639 8.92864V9.02864ZM7.53979 9.44759L7.61053 9.37691L7.6105 9.37688L7.53979 9.44759ZM7.95873 10.181L8.05557 10.156L8.05556 10.156L7.95873 10.181ZM8.61303 12.7191L8.5162 12.744C8.52758 12.7882 8.5674 12.8191 8.61301 12.8191C8.65862 12.8191 8.69845 12.7882 8.70985 12.7441L8.61303 12.7191ZM9.2683 10.181L9.36513 10.206L9.36513 10.206L9.2683 10.181ZM9.68725 9.44759L9.61654 9.37687L9.61654 9.37688L9.68725 9.44759ZM10.4206 9.02864L10.4456 9.12547L10.4456 9.12547L10.4206 9.02864ZM12.9607 8.37337L12.9857 8.4702C13.0298 8.4588 13.0607 8.41896 13.0607 8.37335C13.0607 8.32774 13.0298 8.28791 12.9856 8.27653L12.9607 8.37337ZM10.4206 7.71907L10.3957 7.8159L10.3957 7.81591L10.4206 7.71907ZM9.68725 7.30013L9.61654 7.37084L9.61657 7.37087L9.68725 7.30013ZM9.2683 6.56673H9.1683C9.1683 6.57517 9.16937 6.58358 9.17149 6.59176L9.2683 6.56673ZM9.2683 6.56575H9.3683C9.3683 6.55732 9.36724 6.54893 9.36513 6.54077L9.2683 6.56575ZM8.61303 4.02571L8.70986 4.00073C8.69846 3.95656 8.65863 3.9257 8.61301 3.92571C8.5674 3.92572 8.52757 3.95659 8.51619 4.00077L8.61303 4.02571ZM8.73901 2.3636L8.72277 2.46228C8.79539 2.47422 8.86555 2.49726 8.92957 2.53111L8.97631 2.4427L9.02305 2.3543C8.9389 2.30981 8.84799 2.28019 8.75524 2.26493L8.73901 2.3636ZM8.97631 2.4427L8.92227 2.52685L9.02872 2.59521L9.08276 2.51106L9.13679 2.42692L9.03035 2.35856L8.97631 2.4427ZM9.08276 2.51106L9.01603 2.58555L9.10978 2.66953L9.17651 2.59505L9.24323 2.52056L9.14948 2.43658L9.08276 2.51106ZM9.17651 2.59505L9.1039 2.66381C9.15419 2.71691 9.1959 2.77767 9.22649 2.84333L9.31713 2.8011L9.40778 2.75887C9.3678 2.67307 9.31372 2.5945 9.24911 2.52628L9.17651 2.59505ZM9.31713 2.8011L9.2234 2.83596L9.26735 2.95412L9.36108 2.91927L9.4548 2.88441L9.41086 2.76624L9.31713 2.8011ZM9.36108 2.91927L9.2646 2.94558L9.26753 2.95632L9.36401 2.93001L9.46048 2.9037L9.45755 2.89295L9.36108 2.91927ZM9.36401 2.93001L9.26717 2.95494L10.1392 6.34166L10.2361 6.31673L10.3329 6.29179L9.46085 2.90507L9.36401 2.93001ZM10.2361 6.31673L10.1393 6.34179C10.1708 6.46352 10.2348 6.57505 10.3236 6.6638L10.3943 6.59309L10.465 6.52238C10.4013 6.4587 10.3554 6.37863 10.3329 6.29167L10.2361 6.31673ZM10.3943 6.59309L10.3237 6.6639C10.4127 6.75267 10.5243 6.81585 10.6457 6.84715L10.6706 6.75032L10.6956 6.65349C10.6084 6.63099 10.5284 6.58559 10.4649 6.52229L10.3943 6.59309ZM10.6706 6.75032L10.6457 6.84715L14.0324 7.7202L14.0574 7.62337L14.0823 7.52653L10.6956 6.65349L10.6706 6.75032ZM14.0574 7.62337L14.0331 7.72038L14.0409 7.72233L14.0652 7.62532L14.0894 7.52831L14.0816 7.52635L14.0574 7.62337ZM14.0652 7.62532L14.0385 7.7217C14.1452 7.7512 14.2431 7.80654 14.3228 7.88168L14.3913 7.80891L14.4599 7.73615C14.3566 7.63874 14.23 7.56714 14.0918 7.52894L14.0652 7.62532ZM14.3913 7.80891L14.3169 7.87568L14.4019 7.97041L14.4763 7.90364L14.5508 7.83687L14.4658 7.74215L14.3913 7.80891ZM14.4763 7.90364L14.3922 7.95768L14.4605 8.06412L14.5447 8.01009L14.6288 7.95605L14.5605 7.8496L14.4763 7.90364ZM14.5447 8.01009L14.4563 8.05688C14.5075 8.15358 14.5345 8.2624 14.5345 8.3734L14.6345 8.37337L14.7345 8.37334C14.7345 8.23005 14.6996 8.08908 14.633 7.96329L14.5447 8.01009ZM14.6345 8.37337L14.5345 8.37334C14.5345 8.52149 14.4862 8.66604 14.3967 8.78349L14.4763 8.84407L14.5559 8.90465C14.6721 8.75197 14.7345 8.56476 14.7345 8.3734L14.6345 8.37337ZM14.4763 8.84407L14.3968 8.78346C14.3073 8.90081 14.1812 8.98561 14.0385 9.02503L14.0652 9.12141L14.0918 9.2178C14.2762 9.16686 14.4396 9.05719 14.5558 8.90468L14.4763 8.84407ZM14.0652 9.12141L14.0301 9.02778L14.0223 9.03071L14.0574 9.12434L14.0925 9.21798L14.1003 9.21505L14.0652 9.12141ZM14.0574 9.12434L14.0324 9.0275L10.6457 9.89957L10.6706 9.99641L10.6956 10.0933L14.0823 9.22118L14.0574 9.12434ZM10.6706 9.99641L10.6457 9.89958C10.5237 9.93106 10.4124 9.99513 10.3236 10.0839L10.3943 10.1546L10.465 10.2253C10.5288 10.1615 10.6086 10.1157 10.6956 10.0932L10.6706 9.99641ZM10.3943 10.1546L10.3236 10.0839C10.2347 10.1727 10.1707 10.284 10.1392 10.406L10.2361 10.431L10.3329 10.456C10.3554 10.3689 10.4011 10.2892 10.465 10.2253L10.3943 10.1546ZM10.2361 10.431L10.1392 10.406L9.2662 13.7927L9.36303 13.8177L9.45986 13.8427L10.3329 10.4559L10.2361 10.431ZM9.36303 13.8177L9.26725 13.789L9.26432 13.7987L9.3601 13.8275L9.45588 13.8562L9.45881 13.8464L9.36303 13.8177ZM9.3601 13.8275L9.26381 13.8005C9.22404 13.9425 9.13875 14.0679 9.02132 14.157L9.08178 14.2366L9.14223 14.3163C9.29442 14.2008 9.40487 14.0384 9.45639 13.8544L9.3601 13.8275ZM9.08178 14.2366L9.02136 14.157C8.90412 14.2459 8.76052 14.2938 8.61297 14.2939L8.61303 14.3939L8.61309 14.4939C8.80381 14.4938 8.98988 14.4318 9.14219 14.3163L9.08178 14.2366ZM8.61303 14.3939V14.2939C8.46563 14.2939 8.32202 14.2458 8.20464 14.1569L8.14428 14.2366L8.08392 14.3164C8.23618 14.4316 8.4222 14.4939 8.61303 14.4939V14.3939ZM8.14428 14.2366L8.20472 14.157C8.08732 14.0679 8.00207 13.9425 7.96224 13.8005L7.86596 13.8275L7.76967 13.8545C7.82124 14.0383 7.9316 14.2008 8.08384 14.3163L8.14428 14.2366ZM7.86596 13.8275L7.96174 13.7987L7.95881 13.789L7.86303 13.8177L7.76725 13.8464L7.77018 13.8562L7.86596 13.8275ZM7.86303 13.8177L7.95986 13.7927L7.08682 10.406L6.98998 10.431L6.89315 10.4559L7.7662 13.8427L7.86303 13.8177ZM6.98998 10.431L7.08681 10.406C7.05551 10.2847 6.99233 10.173 6.90356 10.084L6.83276 10.1546L6.76195 10.2252C6.82525 10.2887 6.87065 10.3687 6.89315 10.456L6.98998 10.431ZM6.83276 10.1546L6.90347 10.0839C6.81473 9.99517 6.70312 9.93113 6.58147 9.89961L6.55639 9.99641L6.53131 10.0932C6.61825 10.1157 6.6984 10.1617 6.76205 10.2253L6.83276 10.1546ZM6.55639 9.99641L6.58135 9.89958L3.19463 9.02653L3.16967 9.12337L3.14471 9.2202L6.53143 10.0932L6.55639 9.99641ZM3.16967 9.12337L3.19392 9.02635L3.1822 9.02342L3.15795 9.12044L3.1337 9.21745L3.14542 9.22038L3.16967 9.12337ZM3.15795 9.12044L3.18526 9.02424C3.04412 8.98418 2.91932 8.89824 2.83047 8.78081L2.75072 8.84114L2.67098 8.90147C2.78572 9.05315 2.94715 9.16456 3.13065 9.21664L3.15795 9.12044ZM2.75072 8.84114L2.83051 8.78086C2.74181 8.66345 2.69354 8.52024 2.6935 8.37334L2.5935 8.37337L2.4935 8.3734C2.49355 8.56404 2.55616 8.74951 2.67094 8.90142L2.75072 8.84114ZM2.5935 8.37337L2.6935 8.3734C2.69354 8.22647 2.74179 8.08329 2.83051 7.96588L2.75072 7.90559L2.67094 7.8453C2.55613 7.99723 2.49356 8.18269 2.4935 8.37334L2.5935 8.37337ZM2.75072 7.90559L2.83048 7.96592C2.91911 7.84875 3.04378 7.76363 3.18526 7.72347L3.15795 7.62727L3.13065 7.53107C2.94747 7.58307 2.78593 7.69329 2.67097 7.84527L2.75072 7.90559ZM3.15795 7.62727L3.18957 7.72214L3.20129 7.71824L3.16967 7.62337L3.13805 7.5285L3.12633 7.53241L3.15795 7.62727ZM3.16967 7.62337L3.19463 7.7202L6.58135 6.84715L6.55639 6.75032L6.53143 6.65349L3.14471 7.52653L3.16967 7.62337ZM6.55639 6.75032L6.58801 6.84519L6.66418 6.8198L6.63256 6.72493L6.60094 6.63006L6.52477 6.65545L6.55639 6.75032ZM6.63256 6.72493L6.6702 6.81758C6.75705 6.78229 6.83677 6.72948 6.90344 6.66285L6.83276 6.59212L6.76207 6.52138C6.71427 6.56915 6.65701 6.60706 6.59492 6.63228L6.63256 6.72493ZM6.83276 6.59212L6.9035 6.66279C6.99248 6.57372 7.05544 6.46197 7.08679 6.3408L6.98998 6.31575L6.89317 6.2907C6.87056 6.37807 6.82527 6.45812 6.76201 6.52144L6.83276 6.59212ZM6.98998 6.31575L7.08681 6.34075L7.96083 2.955L7.86401 2.93001L7.76718 2.90501L6.89316 6.29075L6.98998 6.31575ZM7.86401 2.93001L7.96239 2.9479L7.96435 2.93715L7.86596 2.91927L7.76757 2.90138L7.76562 2.91212L7.86401 2.93001ZM7.86596 2.91927L7.96225 2.94626C8.00195 2.80468 8.0873 2.67992 8.20476 2.5907L8.14428 2.51106L8.0838 2.43143C7.932 2.54672 7.82126 2.70828 7.76967 2.89227L7.86596 2.91927ZM8.14428 2.51106L8.2047 2.59075C8.3223 2.50158 8.46663 2.45286 8.61401 2.45286V2.35286V2.25286C8.42279 2.25286 8.23607 2.31598 8.08386 2.43138L8.14428 2.51106ZM8.61401 2.35286L8.60544 2.45249L8.73044 2.46323L8.73901 2.3636L8.74757 2.26397L8.62257 2.25323L8.61401 2.35286ZM7.95873 6.56575H7.85873V6.56673H7.95873H8.05873V6.56575H7.95873ZM7.95873 6.56673L7.86192 6.54168C7.79468 6.80158 7.65817 7.03945 7.46812 7.2294L7.53881 7.30013L7.6095 7.37085C7.82464 7.15583 7.9793 6.88648 8.05555 6.59177L7.95873 6.56673ZM7.53881 7.30013L7.46815 7.22936C7.2784 7.41883 7.04107 7.55416 6.78137 7.62127L6.80639 7.71809L6.83141 7.81491C7.12556 7.7389 7.39442 7.58561 7.60947 7.37089L7.53881 7.30013ZM6.80639 7.71809L6.78141 7.62126L4.24137 8.27654L4.26635 8.37337L4.29133 8.4702L6.83137 7.81492L6.80639 7.71809ZM4.26635 8.37337L4.24136 8.47019L6.78042 9.12547L6.80541 9.02864L6.8304 8.93181L4.29134 8.27654L4.26635 8.37337ZM6.80541 9.02864V9.12864H6.80639V9.02864V8.92864H6.80541V9.02864ZM6.80639 9.02864L6.78135 9.12546C7.04133 9.19269 7.27926 9.32848 7.46908 9.5183L7.53979 9.44759L7.6105 9.37688C7.39537 9.16175 7.12587 9.00797 6.83143 8.93183L6.80639 9.02864ZM7.53979 9.44759L7.46904 9.51826C7.65887 9.70828 7.79484 9.94601 7.8619 10.206L7.95873 10.181L8.05556 10.156C7.97955 9.86135 7.8255 9.5921 7.61053 9.37691L7.53979 9.44759ZM7.95873 10.181L7.8619 10.2059L8.5162 12.744L8.61303 12.7191L8.70986 12.6941L8.05557 10.156L7.95873 10.181ZM8.61303 12.7191L8.70985 12.7441L9.36513 10.206L9.2683 10.181L9.17148 10.156L8.5162 12.6941L8.61303 12.7191ZM9.2683 10.181L9.36513 10.206C9.43227 9.94571 9.56789 9.70837 9.75796 9.5183L9.68725 9.44759L9.61654 9.37688C9.40121 9.5922 9.24755 9.86112 9.17147 10.156L9.2683 10.181ZM9.68725 9.44759L9.75795 9.5183C9.94802 9.32827 10.1854 9.1926 10.4456 9.12547L10.4206 9.02864L10.3957 8.93181C10.1008 9.00788 9.83184 9.1616 9.61654 9.37687L9.68725 9.44759ZM10.4206 9.02864L10.4456 9.12547L12.9857 8.4702L12.9607 8.37337L12.9357 8.27654L10.3957 8.93181L10.4206 9.02864ZM12.9607 8.37337L12.9856 8.27653L10.4456 7.62223L10.4206 7.71907L10.3957 7.81591L12.9357 8.47021L12.9607 8.37337ZM10.4206 7.71907L10.4456 7.62224C10.1857 7.55518 9.94794 7.41921 9.75792 7.22938L9.68725 7.30013L9.61657 7.37087C9.83176 7.58584 10.101 7.73989 10.3957 7.8159L10.4206 7.71907ZM9.68725 7.30013L9.75796 7.22941C9.56811 7.03956 9.43234 6.80171 9.36512 6.5417L9.2683 6.56673L9.17149 6.59176C9.24762 6.88626 9.40138 7.15568 9.61654 7.37084L9.68725 7.30013ZM9.2683 6.56673H9.3683V6.56575H9.2683H9.1683V6.56673H9.2683ZM9.2683 6.56575L9.36513 6.54077L8.70986 4.00073L8.61303 4.02571L8.5162 4.05069L9.17147 6.59073L9.2683 6.56575ZM8.61303 4.02571L8.51619 4.00077L7.86189 6.54081L7.95873 6.56575L8.05557 6.5907L8.70987 4.05066L8.61303 4.02571Z"
            fill="currentColor"
          />
        </svg>
      </div>

      <!-- Header Text -->
      <div
        class="text-center mb-12"
      >
        <h2 class="text-2xl font-semibold text-gray-900 mb-3">
          How can I help you today?
        </h2>
        <p class="text-gray-600 max-w-md mx-auto leading-relaxed">
          Choose a suggestion below or ask me anything about coding, design, or
          creative projects.
        </p>
      </div>

      <!-- Suggestion Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-12 w-full max-w-2xl">
        <button
          v-for="(suggestion, index) in displayedSuggestions"
          :key="suggestion.title"
          @click="useSuggestion(suggestion)"
          class="group p-6 text-left border border-gray-200 rounded-2xl hover:border-gray-300 hover:shadow-sm transition-all duration-300 bg-white animate-slideUp opacity-0"
        >
          <div class="flex items-start space-x-4">
            <div
              class="p-2 bg-gray-50 rounded-xl group-hover:bg-gray-100 transition-colors duration-300"
            >
              <component :is="suggestion.icon" class="w-5 h-5 text-gray-600" />
            </div>
            <div class="flex-1">
              <h3 class="font-medium text-gray-900 mb-2">
                {{ suggestion.title }}
              </h3>
              <p class="text-sm text-gray-600 leading-relaxed">
                {{ suggestion.prompt }}
              </p>
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- Chat Messages (shown when conversation starts) -->
      <div
        v-if="messages.length > 0"
        ref="chatContainer"
        class="absolute inset-0 overflow-y-auto space-y-6 p-6 animate-fadeIn opacity-0"
      >
        <div
          v-for="message in messages"
          :key="message.id"
          class="animate-messageIn"
        >
          <div v-if="message.role === 'user'" class="flex justify-end">
            <div
              class="max-w-[80%] p-4 bg-gray-900 text-white rounded-2xl rounded-br-md"
            >
              <div
                class="text-sm leading-relaxed markdown-content"
                v-html="renderMarkdown(message.content)"
              ></div>
            </div>
          </div>

          <div v-else class="flex justify-start">
            <div
              class="max-w-[80%] p-4 bg-gray-50 text-gray-900 rounded-2xl rounded-bl-md"
            >
              <div
                class="text-sm leading-relaxed markdown-content"
                v-html="renderMarkdown(message.content)"
              ></div>
            </div>
          </div>
        </div>

        <!-- Loading indicator -->
        <div v-if="loading" class="flex justify-start animate-messageIn">
          <div class="bg-gray-50 text-gray-900 rounded-2xl rounded-bl-md p-4">
            <div class="flex items-center space-x-2">
              <div class="flex space-x-1">
                <div
                  class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                ></div>
                <div
                  class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style="animation-delay: 0.1s"
                ></div>
                <div
                  class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style="animation-delay: 0.2s"
                ></div>
              </div>
              <span class="text-sm text-gray-500">Thinking...</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Input Area -->
    <div
      class="p-6 border-t border-gray-100 animate-slideUp opacity-0"
    >
      <div class="max-w-4xl mx-auto">
        <div class="flex items-center relative">
          <textarea
            v-model="newMessage"
            @keydown.enter.prevent="!$event.shiftKey && sendMessage()"
            placeholder="Ask me anything..."
            class="w-full p-4 pr-14 border border-gray-200 rounded-2xl resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm placeholder-gray-500 bg-white shadow-sm"
            rows="1"
            :disabled="loading"
            style="min-height: 56px; max-height: 120px; outline: none"
          ></textarea>

          <button
            @click="sendMessage()"
            :disabled="!newMessage.trim() || loading"
            class="absolute right-2 p-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 disabled:hover:bg-blue-600"
          >
            <Send class="w-4 h-4" />
          </button>
        </div>

        <!-- Disclaimer -->
        <p class="text-xs text-gray-500 mt-3 text-center">
          AI can make mistakes. Consider checking important information.
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.markdown-content :deep(a) {
  color: #2563eb;
  text-decoration: underline;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes messageIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideUp {
  animation: slideUp 0.6s ease-out forwards;
  animation-fill-mode: forwards;
}

.animate-fadeIn {
  animation: fadeIn 0.4s ease-out forwards;
  animation-fill-mode: forwards;
}

.animate-messageIn {
  animation: messageIn 0.3s ease-out;
}

.animate-visible {
  animation-play-state: running;
}

/* Ensure elements are hidden before animation */
.opacity-0 {
  opacity: 0;
}

/* Custom scrollbar for chat container */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #e5e7eb;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #d1d5db;
}

/* Auto-resize textarea */
textarea {
  field-sizing: content;
}
</style>
