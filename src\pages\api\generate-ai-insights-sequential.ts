import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { 
  generateAIProsConsInsights, 
  generateAILeadInsights, 
  generateAIPerformanceInsights,
  generateAIPageSummary,
  generateAIPerformanceSEOSummary
} from '../../lib/ai-contextual-analyzer';
import { log } from '../../lib/logger';

export const prerender = false;

interface GenerationStep {
  stepNumber: number;
  stepName: string;
  description: string;
  handler: (analysis: any) => Promise<any>;
  storeHandler: (analysisId: string, data: any) => Promise<void>;
  markComplete: string;
}

export const POST: APIRoute = async ({ request }) => {
  try {
    const { analysisId } = await request.json();

    log.info(`Starting sequential AI insights generation for analysis: ${analysisId}`);

    if (!analysisId) {
      log.warn('Missing analysisId in sequential AI insights request');
      return new Response(JSON.stringify({ error: 'Missing analysisId' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Fetch analysis data
    const { data: analysis, error: fetchError } = await supabaseAdmin
      .from('analyses')
      .select('*')
      .eq('id', analysisId)
      .single();

    if (fetchError || !analysis) {
      log.error(`Failed to fetch analysis: ${fetchError?.message}`);
      return new Response(JSON.stringify({ error: 'Analysis not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Update generation status to in_progress
    await supabaseAdmin
      .from('analyses')
      .update({ 
        generation_status: 'in_progress',
        generation_started_at: new Date().toISOString()
      })
      .eq('id', analysisId);

    // Define generation steps
    const generationSteps: GenerationStep[] = [
      {
        stepNumber: 1,
        stepName: 'Extracting Website Context',
        description: 'Extracting and analyzing website content and structure',
        handler: async (analysis) => {
          // Context extraction is already done in the database trigger
          return { success: true };
        },
        storeHandler: async (analysisId, data) => {
          // Context is already stored by the trigger
        },
        markComplete: 'context_extracted'
      },
      {
        stepNumber: 2,
        stepName: 'Generating Page Summary',
        description: 'Generating comprehensive page summary and business insights',
        handler: generateAIPageSummary,
        storeHandler: async (analysisId, data) => {
          await supabaseAdmin
            .from('ai_page_summaries')
            .upsert({
              analysis_id: analysisId,
              page_purpose: data.pagePurpose,
              key_takeaways: data.keyTakeaways,
              hero_effectiveness: data.heroEffectiveness,
              value_proposition_clarity: data.valuePropositionClarity,
              lead_capture_assessment: data.leadCaptureAssessment,
              user_journey_analysis: data.userJourneyAnalysis,
              business_impact_summary: data.businessImpactSummary,
              conversion_assessment: data.conversionAssessment || '',
              trust_signals_analysis: data.trustSignalsAnalysis || '',
              mobile_experience_notes: data.mobileExperienceNotes || '',
              competitive_advantages: data.competitiveAdvantages || [],
              improvement_priorities: data.improvementPriorities || [],
              business_model_insights: data.businessModelInsights || '',
              target_audience_analysis: data.targetAudienceAnalysis || '',
              content_strategy_notes: data.contentStrategyNotes || ''
            });
        },
        markComplete: 'page_summary_enhanced'
      },
      {
        stepNumber: 3,
        stepName: 'Analyzing Pros & Cons',
        description: 'Analyzing website strengths and weaknesses for conversion optimization',
        handler: generateAIProsConsInsights,
        storeHandler: async (analysisId, data) => {
          // Get insight categories
          const { data: categories } = await supabaseAdmin
            .from('insight_categories')
            .select('id, name');

          const categoryMap = new Map(categories?.map(cat => [cat.name, cat.id]) || []);

          // Store strengths
          if (data.strengths && data.strengths.length > 0) {
            const strengthsToInsert = data.strengths.map((strength: any, index: number) => ({
              analysis_id: analysisId,
              category_id: categoryMap.get(strength.category) || categoryMap.get('conversion'),
              insight_type: 'strength',
              title: strength.title,
              description: strength.description,
              evidence: strength.evidence,
              impact_explanation: strength.impactExplanation,
              implementation_steps: strength.implementationSteps,
              business_value: strength.businessValue,
              priority_score: strength.priorityScore,
              confidence_score: strength.confidenceScore,
              display_order: index,
              impact_score: Math.round(strength.priorityScore / 10),
              effort_required: strength.effortRequired || 'Medium',
              timeline_estimate: strength.timelineEstimate || 'To be determined',
              ai_model: 'qwen-2.5-72b'
            }));

            await supabaseAdmin
              .from('ai_insights')
              .upsert(strengthsToInsert);
          }

          // Store weaknesses
          if (data.weaknesses && data.weaknesses.length > 0) {
            const weaknessesToInsert = data.weaknesses.map((weakness: any, index: number) => ({
              analysis_id: analysisId,
              category_id: categoryMap.get(weakness.category) || categoryMap.get('conversion'),
              insight_type: 'weakness',
              title: weakness.title,
              description: weakness.description,
              evidence: weakness.evidence,
              impact_explanation: weakness.impactExplanation,
              implementation_steps: weakness.implementationSteps,
              business_value: weakness.businessValue,
              priority_score: weakness.priorityScore,
              confidence_score: weakness.confidenceScore,
              display_order: index,
              impact_score: Math.round(weakness.priorityScore / 10),
              effort_required: weakness.effortRequired || 'Medium',
              timeline_estimate: weakness.timelineEstimate || 'To be determined',
              ai_model: 'qwen-2.5-72b'
            }));

            await supabaseAdmin
              .from('ai_insights')
              .upsert(weaknessesToInsert);
          }
        },
        markComplete: 'pros_cons_generated'
      },
      {
        stepNumber: 4,
        stepName: 'Creating Lead Insights',
        description: 'Creating lead qualification questions and business insights',
        handler: generateAILeadInsights,
        storeHandler: async (analysisId, data) => {
          if (data.leadQuestions && data.leadQuestions.length > 0) {
            const leadInsightsToInsert = data.leadQuestions.map((question: any) => ({
              analysis_id: analysisId,
              question_type: question.questionType,
              question_text: question.questionText,
              context_explanation: question.contextExplanation,
              qualification_value: question.qualificationValue,
              suggested_response: question.suggestedResponse,
              priority_level: question.priorityLevel,
              business_impact_score: question.businessImpactScore || Math.round(question.priorityLevel * 2),
              conversion_relevance: question.conversionRelevance || 'High relevance to conversion process',
              follow_up_questions: question.followUpQuestions || [],
              objection_handling: question.objectionHandling || '',
              qualification_criteria: question.qualificationCriteria || '',
              lead_scoring_weight: question.leadScoringWeight || 1.0
            }));

            await supabaseAdmin
              .from('lead_qualification_insights')
              .upsert(leadInsightsToInsert);
          }

          // Update analysis with business context
          await supabaseAdmin
            .from('analyses')
            .update({
              website_business_type: data.businessType,
              ai_target_audience: data.targetAudience,
              ai_value_proposition: data.valueProposition,
              conversion_barriers: data.conversionBarriers || []
            })
            .eq('id', analysisId);
        },
        markComplete: 'lead_insights_generated'
      },
      {
        stepNumber: 5,
        stepName: 'Analyzing Performance Impact',
        description: 'Analyzing performance metrics and optimization opportunities',
        handler: generateAIPerformanceInsights,
        storeHandler: async (analysisId, data) => {
          // Store primary issue as critical insight
          if (data.primaryIssue) {
            await supabaseAdmin
              .from('performance_impact_insights')
              .upsert({
                analysis_id: analysisId,
                impact_type: 'critical',
                metric_name: data.primaryIssue.title,
                current_value: 0,
                target_value: 100,
                impact_description: data.primaryIssue.description,
                conversion_impact: data.primaryIssue.conversionImpact,
                implementation_guide: data.primaryIssue.evidence,
                expected_improvement: 'Significant performance improvement expected',
                effort_level: 'High',
                timeline: 'To be determined'
              });
          }

          // Store key findings as optimization insights
          if (data.keyFindings && data.keyFindings.length > 0) {
            const performanceInsightsToInsert = data.keyFindings.map((finding: any) => ({
              analysis_id: analysisId,
              impact_type: 'optimization',
              metric_name: finding.metric,
              current_value: parseFloat(finding.currentValue) || 0,
              target_value: parseFloat(finding.targetValue) || 0,
              impact_description: finding.impact,
              conversion_impact: data.businessImpact,
              implementation_guide: 'Implementation guide available',
              expected_improvement: 'Performance improvement expected',
              effort_level: 'Medium',
              timeline: 'To be determined'
            }));

            await supabaseAdmin
              .from('performance_impact_insights')
              .upsert(performanceInsightsToInsert);
          }

          // Store recommendations as opportunity insights
          if (data.recommendations && data.recommendations.length > 0) {
            const recommendationInsights = data.recommendations.map((rec: any) => ({
              analysis_id: analysisId,
              impact_type: 'opportunity',
              metric_name: rec.title,
              current_value: 0,
              target_value: 100,
              impact_description: rec.description,
              conversion_impact: rec.expectedImprovement,
              implementation_guide: rec.description,
              expected_improvement: rec.expectedImprovement,
              effort_level: rec.priority === 'High' ? 'High' : rec.priority === 'Low' ? 'Low' : 'Medium',
              timeline: 'To be determined'
            }));

            await supabaseAdmin
              .from('performance_impact_insights')
              .upsert(recommendationInsights);
          }
        },
        markComplete: 'performance_insights_generated'
      },
      {
        stepNumber: 6,
        stepName: 'Generating SEO Insights',
        description: 'Generating SEO recommendations and technical insights',
        handler: async (analysis) => {
          // Generate comprehensive SEO insights
          const seoInsights = await generateAIPerformanceSEOSummary(analysis);
          return {
            
          };
        },
        storeHandler: async (analysisId, data) => {
          await supabaseAdmin
            .from('ai_seo_insights')
            .upsert({
              analysis_id: analysisId,
              meta_optimization_notes: data.metaOptimizationNotes,
              content_seo_assessment: data.contentSeoAssessment,
              technical_seo_issues: data.technicalSeoIssues,
              keyword_opportunities: data.keywordOpportunities,
              local_seo_recommendations: data.localSeoRecommendations,
              schema_markup_suggestions: data.schemaMarkupSuggestions,
              internal_linking_analysis: data.internalLinkingAnalysis,
              page_speed_seo_impact: data.pageSpeedSeoImpact,
              mobile_seo_assessment: data.mobileSeoAssessment,
              competitor_analysis_notes: data.competitorAnalysisNotes,
              content_gap_analysis: data.contentGapAnalysis,
              seo_priority_actions: data.seoPriorityActions,
              expected_traffic_impact: data.expectedTrafficImpact
            });
        },
        markComplete: 'seo_insights_generated'
      },
      {
        stepNumber: 7,
        stepName: 'Creating Chat Context',
        description: 'Preparing AI chat context and suggested conversation starters',
        handler: async (analysis) => {
          // Generate chat context based on all previous analysis
          const websiteSummary = `${analysis.title} - Conversion Score: ${analysis.score}/10`;
          const keyMetrics = {
            conversionScore: analysis.score,
            performanceScore: analysis.performance_score,
            seoScore: analysis.seo_score,
            leadGenerationScore: analysis.lead_generation_score
          };

          const businessContext = `Business Type: ${analysis.website_business_type || 'General Business'}, Target Audience: ${analysis.ai_target_audience || 'General Audience'}`;

          const suggestedPrompts = [
            "What are the biggest conversion barriers on this website?",
            "How can I improve the performance score?",
            "What SEO improvements should I prioritize?",
            "What questions might my leads have after visiting this page?",
            "How does this website compare to industry standards?",
            "What's the quickest win for improving conversions?"
          ];

          const conversationStarters = [
            "Let's discuss your website's conversion optimization opportunities",
            "I can help you understand your performance metrics",
            "Want to explore your SEO improvement potential?",
            "Let's analyze your lead generation strategy",
            "I can explain your website's strengths and weaknesses"
          ];

          const optimizationOpportunities = [
            "Conversion rate optimization based on identified weaknesses",
            "Performance improvements for better user experience",
            "SEO enhancements for increased visibility",
            "Lead capture optimization strategies",
            "Trust signal improvements"
          ];

          return {
            websiteSummary,
            keyMetrics,
            businessContext,
            suggestedPrompts,
            conversationStarters,
            technicalContext: {
              performanceData: analysis.lighthouse_data,
              seoData: analysis.seo_data
            },
            optimizationOpportunities
          };
        },
        storeHandler: async (analysisId, data) => {
          await supabaseAdmin
            .from('ai_chat_context')
            .upsert({
              analysis_id: analysisId,
              website_summary: data.websiteSummary,
              key_metrics: data.keyMetrics,
              business_context: data.businessContext,
              suggested_prompts: data.suggestedPrompts,
              conversation_starters: data.conversationStarters,
              technical_context: data.technicalContext,
              optimization_opportunities: data.optimizationOpportunities
            });
        },
        markComplete: 'chat_context_generated'
      },
      {
        stepNumber: 8,
        stepName: 'Finalizing Analysis',
        description: 'Completing analysis and marking all content as generated',
        handler: async (analysis) => {
          return { success: true };
        },
        storeHandler: async (analysisId, data) => {
          // Mark all content as generated and update final status
          await supabaseAdmin
            .from('analyses')
            .update({
              generation_status: 'completed',
              generation_completed_at: new Date().toISOString(),
              all_content_generated: true,
              ai_insights_generated: true,
              ai_summary_generated: true
            })
            .eq('id', analysisId);
        },
        markComplete: 'all_content_generated'
      }
    ];

    // Execute steps sequentially
    for (const step of generationSteps) {
      try {
        log.info(`Starting step ${step.stepNumber}: ${step.stepName}`);
        
        // Update progress to in_progress
        await supabaseAdmin.rpc('update_generation_progress', {
          p_analysis_id: analysisId,
          p_step_number: step.stepNumber,
          p_status: 'in_progress'
        });

        // Execute the step
        const result = await step.handler(analysis);
        
        // Store the result
        await step.storeHandler(analysisId, result);
        
        // Mark content as generated
        await supabaseAdmin.rpc('mark_content_generated', {
          p_analysis_id: analysisId,
          p_content_type: step.markComplete
        });

        // Update progress to completed
        await supabaseAdmin.rpc('update_generation_progress', {
          p_analysis_id: analysisId,
          p_step_number: step.stepNumber,
          p_status: 'completed'
        });

        log.info(`Completed step ${step.stepNumber}: ${step.stepName}`);
        
      } catch (error) {
        log.error(`Error in step ${step.stepNumber}: ${error}`);
        
        // Update progress to failed
        await supabaseAdmin.rpc('update_generation_progress', {
          p_analysis_id: analysisId,
          p_step_number: step.stepNumber,
          p_status: 'failed',
          p_error_message: error instanceof Error ? error.message : 'Unknown error'
        });
        
        throw error;
      }
    }

    log.info(`Sequential AI insights generation completed for analysis: ${analysisId}`);

    return new Response(JSON.stringify({
      success: true,
      message: 'Sequential AI insights generation completed successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`Error in sequential AI insights generation: ${error}`);
    
    return new Response(JSON.stringify({
      error: 'Failed to generate AI insights sequentially',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
