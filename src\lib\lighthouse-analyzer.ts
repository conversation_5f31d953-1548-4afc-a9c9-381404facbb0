import { log } from './logger';

export interface LighthouseMetrics {
  performanceScore: number;
  accessibilityScore: number;
  bestPracticesScore: number;
  seoScore: number;
  coreWebVitals: {
    lcp: number;
    fid: number;
    cls: number;
  };
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
  conversionImpact: string;
  recommendations: Array<{
    title: string;
    description: string;
    impact: 'High' | 'Medium' | 'Low';
    effort: 'High' | 'Medium' | 'Low';
    category: string;
  }>;
}

export interface SEOAnalysisResult {
  score: number;
  issues: Array<{
    issue: string;
    recommendation: string;
    severity_score: number;
  }>;
  data: {
    title?: string;
    metaDescription?: string;
    canonicalUrl?: string;
    openGraphTags?: Record<string, string>;
    structuredData?: any[];
    headingStructure?: Array<{ level: number; text: string }>;
    keywordDensity?: Record<string, number>;
  };
  pros: string[];
  cons: string[];
}

// Core Web Vitals thresholds (Google standards)
export const CORE_WEB_VITALS_THRESHOLDS = {
  lcp: { good: 2.5, poor: 4.0 }, // seconds
  fid: { good: 100, poor: 300 }, // milliseconds
  cls: { good: 0.1, poor: 0.25 } // score
};

export function evaluateMetric(value: number, thresholds: { good: number; poor: number }): 'good' | 'needs-improvement' | 'poor' {
  if (value <= thresholds.good) return 'good';
  if (value <= thresholds.poor) return 'needs-improvement';
  return 'poor';
}

function calculateGrade(score: number): 'A' | 'B' | 'C' | 'D' | 'F' {
  if (score >= 90) return 'A';
  if (score >= 80) return 'B';
  if (score >= 70) return 'C';
  if (score >= 60) return 'D';
  return 'F';
}

function getConversionImpactMessage(score: number): string {
  if (score >= 90) return 'Excellent performance with potential for 2-5% conversion improvement';
  if (score >= 80) return 'Good performance with potential for 5-15% conversion improvement';
  if (score >= 70) return 'Fair performance with potential for 15-25% conversion improvement';
  if (score >= 60) return 'Poor performance with potential for 25-40% conversion improvement';
  return 'Critical performance issues with potential for 40%+ conversion improvement';
}

export async function runLighthouseAnalysis(url: string): Promise<LighthouseMetrics> {
  try {
    log.info(`Starting Lighthouse analysis for ${url}`);

    // Dynamic import to avoid build issues
    const puppeteer = await import('puppeteer');
    const lighthouse = await import('lighthouse');

    // Launch browser with optimized settings
    const browser = await puppeteer.default.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    try {
      // Run Lighthouse audit
      const lighthouseResult = await lighthouse.default(url, {
        port: parseInt(new URL(browser.wsEndpoint()).port, 10),
        output: 'json',
        logLevel: 'error',
        onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
      }, {
        extends: 'lighthouse:default',
        settings: {
          maxWaitForFcp: 15 * 1000,
          maxWaitForLoad: 35 * 1000,
          formFactor: 'mobile', // Fixed: consistent with mobile analysis requirements
          throttling: {
            rttMs: 40,
            throughputKbps: 10240,
            cpuSlowdownMultiplier: 1
          }
        }
      });

      if (!lighthouseResult) {
        throw new Error('Lighthouse analysis returned no result.');
      }

      const { lhr } = lighthouseResult;

      // Extract scores
      const performanceScore = Math.round((lhr.categories.performance?.score || 0) * 100);
      const accessibilityScore = Math.round((lhr.categories.accessibility?.score || 0) * 100);
      const bestPracticesScore = Math.round((lhr.categories['best-practices']?.score || 0) * 100);
      const seoScore = Math.round((lhr.categories.seo?.score || 0) * 100);

      // Extract Core Web Vitals
      const lcpScore = (lhr.audits['largest-contentful-paint']?.numericValue || 0) / 1000;
      const fidScore = lhr.audits['max-potential-fid']?.numericValue || 0;
      const clsScore = lhr.audits['cumulative-layout-shift']?.numericValue || 0;

      const grade = calculateGrade(performanceScore);
      const conversionImpact = getConversionImpactMessage(performanceScore);

      // Generate performance recommendations
      const recommendations = [];
      
      if (performanceScore < 80) {
        recommendations.push({
          title: 'Optimize Page Load Speed',
          description: 'Improve server response times and optimize resource loading',
          impact: 'High' as const,
          effort: 'Medium' as const,
          category: 'Performance'
        });
      }

      if (lcpScore > 2.5) {
        recommendations.push({
          title: 'Improve Largest Contentful Paint',
          description: 'Optimize images and critical rendering path to reduce LCP',
          impact: 'High' as const,
          effort: 'Medium' as const,
          category: 'Core Web Vitals'
        });
      }

      if (clsScore > 0.1) {
        recommendations.push({
          title: 'Reduce Layout Shift',
          description: 'Set explicit dimensions for images and ads to prevent layout shifts',
          impact: 'Medium' as const,
          effort: 'Low' as const,
          category: 'Core Web Vitals'
        });
      }

      await browser.close();

      log.info(`Lighthouse analysis completed for ${url}. Performance: ${performanceScore}`);

      return {
        performanceScore,
        accessibilityScore,
        bestPracticesScore,
        seoScore,
        coreWebVitals: {
          lcp: Math.round(lcpScore * 100) / 100,
          fid: Math.round(fidScore),
          cls: Math.round(clsScore * 1000) / 1000
        },
        grade,
        conversionImpact,
        recommendations
      };

    } finally {
      await browser.close();
    }

  } catch (error) {
    log.error(`Lighthouse analysis failed: ${error instanceof Error ? error.message : error}`);
    throw new Error(`Lighthouse analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function runSEOAnalysis(url: string): Promise<SEOAnalysisResult> {
  try {
    log.info(`Starting SEO analysis for ${url}`);

    const puppeteer = await import('puppeteer');
    const browser = await puppeteer.default.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
      const page = await browser.newPage();
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });

      // Extract SEO data
      const seoData = await page.evaluate(() => {
        const title = document.querySelector('title')?.textContent?.trim() || '';
        const metaDescription = document.querySelector('meta[name="description"]')?.getAttribute('content')?.trim() || '';
        const canonical = document.querySelector('link[rel="canonical"]')?.getAttribute('href') || '';

        // Open Graph tags
        const ogTags: Record<string, string> = {};
        document.querySelectorAll('meta[property^="og:"]').forEach(tag => {
          const property = tag.getAttribute('property');
          const content = tag.getAttribute('content');
          if (property && content) {
            ogTags[property] = content;
          }
        });

        // Heading structure
        const headings: Array<{level: number, text: string}> = [];
        document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(heading => {
          headings.push({
            level: parseInt(heading.tagName.charAt(1)),
            text: heading.textContent?.trim() || ''
          });
        });

        // Images analysis
        const imagesWithoutAlt = Array.from(document.querySelectorAll('img')).filter(img => !img.getAttribute('alt')).length;
        const totalImages = document.querySelectorAll('img').length;

        // Structured data
        const structuredData: any[] = [];
        document.querySelectorAll('script[type="application/ld+json"]').forEach(script => {
          try {
            const data = JSON.parse(script.textContent || '');
            structuredData.push(data);
          } catch (e) {
            // Invalid JSON-LD
          }
        });

        return {
          title,
          metaDescription,
          canonical,
          ogTags,
          headings,
          structuredData,
          bodyText: document.body.textContent || '',
          imagesWithoutAlt,
          totalImages,
          hasH1: headings.some(h => h.level === 1),
          h1Count: headings.filter(h => h.level === 1).length
        };
      });

      await browser.close();

      // Generate SEO issues
      const issues = [];
      const pros = [];
      const cons = [];

      // Title analysis
      if (!seoData.title) {
        issues.push({
          issue: 'Missing Title Tag',
          recommendation: 'Add a descriptive title tag (50-60 characters)',
          severity_score: 5
        });
        cons.push('Missing title tag');
      } else {
        if (seoData.title.length >= 30 && seoData.title.length <= 60) {
          pros.push('Well-optimized title tag length');
        } else if (seoData.title.length < 30) {
          issues.push({
            issue: 'Title Tag Too Short',
            recommendation: `Expand title to 50-60 characters (currently ${seoData.title.length})`,
            severity_score: 3
          });
          cons.push('Title tag too short');
        } else {
          issues.push({
            issue: 'Title Tag Too Long',
            recommendation: `Shorten title to under 60 characters (currently ${seoData.title.length})`,
            severity_score: 3
          });
          cons.push('Title tag too long');
        }
      }

      // Meta description analysis
      if (!seoData.metaDescription) {
        issues.push({
          issue: 'Missing Meta Description',
          recommendation: 'Add a compelling meta description (150-160 characters)',
          severity_score: 3
        });
        cons.push('Missing meta description');
      } else {
        if (seoData.metaDescription.length <= 160) {
          pros.push('Good meta description length');
        } else {
          issues.push({
            issue: 'Meta Description Too Long',
            recommendation: `Shorten meta description to under 160 characters (currently ${seoData.metaDescription.length})`,
            severity_score: 2
          });
          cons.push('Meta description too long');
        }
      }

      // H1 analysis
      if (!seoData.hasH1) {
        issues.push({
          issue: 'Missing H1 Tag',
          recommendation: 'Add a single, descriptive H1 tag to the page',
          severity_score: 4
        });
        cons.push('Missing H1 tag');
      } else if (seoData.h1Count === 1) {
        pros.push('Proper H1 tag usage');
      } else {
        issues.push({
          issue: 'Multiple H1 Tags',
          recommendation: `Use only one H1 tag per page (currently ${seoData.h1Count})`,
          severity_score: 3
        });
        cons.push('Multiple H1 tags');
      }

      // Images analysis
      if (seoData.imagesWithoutAlt > 0) {
        issues.push({
          issue: 'Images Missing Alt Text',
          recommendation: `Add alt text to ${seoData.imagesWithoutAlt} images for accessibility and SEO`,
          severity_score: 3
        });
        cons.push('Images missing alt text');
      } else if (seoData.totalImages > 0) {
        pros.push('All images have alt text');
      }

      // Structured data analysis
      if (seoData.structuredData.length > 0) {
        pros.push('Structured data implementation');
      } else {
        issues.push({
          issue: 'No Structured Data',
          recommendation: 'Add JSON-LD structured data for better search visibility',
          severity_score: 2
        });
        cons.push('No structured data');
      }

      // Calculate SEO score
      let score = 100;
      issues.forEach(issue => {
        if (issue.severity_score >= 5) score -= 20;
        else if (issue.severity_score >= 4) score -= 15;
        else if (issue.severity_score >= 3) score -= 10;
        else score -= 5;
      });

      log.info(`SEO analysis completed for ${url}. Score: ${Math.max(0, score)}`);

      return {
        score: Math.max(0, score),
        issues,
        data: {
          title: seoData.title,
          metaDescription: seoData.metaDescription,
          canonicalUrl: seoData.canonical,
          openGraphTags: seoData.ogTags,
          structuredData: seoData.structuredData,
          headingStructure: seoData.headings,
          keywordDensity: {}
        },
        pros,
        cons
      };

    } finally {
      await browser.close();
    }

  } catch (error) {
    log.error(`SEO analysis failed: ${error instanceof Error ? error.message : error}`);
    throw new Error(`SEO analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function captureScreenshot(url: string): Promise<Buffer> {
  let browser;
  try {
    log.info(`[Screenshot] Capturing for ${url}`);
    const puppeteer = await import('puppeteer');

    log.info('[Screenshot] Launching browser...');
    browser = await puppeteer.default.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'],
    });

    log.info('[Screenshot] Browser launched. Opening new page...');
    const page = await browser.newPage();

    const viewport = { width: 1280, height: 800 };
    log.info(`[Screenshot] Setting viewport to ${viewport.width}x${viewport.height}`);
    await page.setViewport(viewport);

    log.info(`[Screenshot] Navigating to ${url}...`);
    await page.goto(url, { waitUntil: 'networkidle2', timeout: 45000 });
    log.info('[Screenshot] Page loaded.');

    const screenshotOptions = {
      type: 'png' as const,
      fullPage: false,
    };
    log.info(`[Screenshot] Taking screenshot with options: ${JSON.stringify(screenshotOptions)}`);
    const screenshot = await page.screenshot(screenshotOptions);

    log.info(`[Screenshot] Capture successful for ${url}`);
    return screenshot as Buffer;

  } catch (error) {
    log.error(`[Screenshot] Capture failed for ${url}: ${error instanceof Error ? error.message : error}`);
    throw new Error(`[Screenshot] Capture failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    if (browser) {
      log.info('[Screenshot] Closing browser.');
      await browser.close();
    }
  }
}