import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { log } from '../../lib/logger';

export const prerender = false;

const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { analysisId } = await request.json();

    if (!analysisId) {
      return new Response(JSON.stringify({ error: 'Missing analysisId' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    log.info(`Generating advanced metrics summary for analysis: ${analysisId}`);

    // Check if summary already exists
    const { data: existingSummary } = await supabaseAdmin
      .from('advanced_metrics_summary')
      .select('*')
      .eq('analysis_id', analysisId)
      .single();

    if (existingSummary) {
      log.info('Advanced metrics summary already exists, returning cached version');
      return new Response(JSON.stringify({ 
        success: true, 
        summary: existingSummary,
        cached: true 
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Fetch analysis data
    const { data: analysis, error: analysisError } = await supabaseAdmin
      .from('analyses')
      .select('*')
      .eq('id', analysisId)
      .single();

    if (analysisError || !analysis) {
      log.error(`Failed to fetch analysis: ${analysisError?.message}`);
      return new Response(JSON.stringify({ error: 'Analysis not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate AI-powered advanced metrics summary
    const summary = await generateAdvancedMetricsSummary(analysis);

    // Store in database
    const { data: savedSummary, error: saveError } = await supabaseAdmin
      .from('advanced_metrics_summary')
      .upsert({
        analysis_id: analysisId,
        lighthouse_summary: summary.lighthouseSummary,
        performance_breakdown: summary.performanceBreakdown,
        accessibility_insights: summary.accessibilityInsights,
        best_practices_analysis: summary.bestPracticesAnalysis,
        seo_technical_summary: summary.seoTechnicalSummary,
        mobile_performance_notes: summary.mobilePerformanceNotes,
        optimization_priorities: summary.optimizationPriorities,
        business_impact_assessment: summary.businessImpactAssessment,
        technical_debt_analysis: summary.technicalDebtAnalysis,
        monitoring_recommendations: summary.monitoringRecommendations
      })
      .select()
      .single();

    if (saveError) {
      log.error(`Failed to save advanced metrics summary: ${saveError.message}`);
      return new Response(JSON.stringify({ error: 'Failed to save summary' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    log.info('Advanced metrics summary generated and saved successfully');

    return new Response(JSON.stringify({ 
      success: true, 
      summary: savedSummary,
      cached: false 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`Error generating advanced metrics summary: ${error}`);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

async function generateAdvancedMetricsSummary(analysis: any) {
  const lighthouseData = analysis.lighthouse_data ? 
    (typeof analysis.lighthouse_data === 'string' ? 
      JSON.parse(analysis.lighthouse_data) : 
      analysis.lighthouse_data) : {};

  const prompt = `You are a web performance expert. Analyze the following website performance data and provide a comprehensive advanced metrics summary.

Website: ${analysis.url}
Performance Score: ${analysis.performance_score || 'N/A'}
Accessibility Score: ${lighthouseData.accessibilityScore || 'N/A'}
Best Practices Score: ${lighthouseData.bestPracticesScore || 'N/A'}
SEO Score: ${analysis.seo_score || 'N/A'}
LCP: ${analysis.lcp_score || 'N/A'}s
FID: ${analysis.fid_score || 'N/A'}ms
CLS: ${analysis.cls_score || 'N/A'}

Lighthouse Data: ${JSON.stringify(lighthouseData, null, 2)}

Provide a detailed analysis in the following JSON format:
{
  "lighthouseSummary": "Overall summary of Lighthouse findings",
  "performanceBreakdown": {
    "coreWebVitals": "Analysis of LCP, FID, CLS",
    "loadingMetrics": "First Contentful Paint, Speed Index analysis",
    "interactivityMetrics": "Time to Interactive, Total Blocking Time",
    "visualStability": "Cumulative Layout Shift analysis"
  },
  "accessibilityInsights": "Key accessibility issues and improvements",
  "bestPracticesAnalysis": "Security, modern standards, and best practices review",
  "seoTechnicalSummary": "Technical SEO factors affecting performance",
  "mobilePerformanceNotes": "Mobile-specific performance considerations",
  "optimizationPriorities": [
    "Priority 1: Most critical optimization",
    "Priority 2: Secondary optimization",
    "Priority 3: Nice-to-have improvement"
  ],
  "businessImpactAssessment": "How performance affects business metrics and conversions",
  "technicalDebtAnalysis": "Technical debt and legacy code impact on performance",
  "monitoringRecommendations": [
    "Monitoring tool recommendation 1",
    "Key metric to track",
    "Alert threshold suggestion"
  ]
}

Focus on actionable insights and specific recommendations.`;

  try {
    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3.5-sonnet',
        messages: [
          {
            role: "system",
            content: "You are a web performance expert specializing in Lighthouse metrics, Core Web Vitals, and performance optimization. Provide detailed, actionable insights."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 4000,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`Advanced metrics summary generated for ${analysis.url}`);
    
    return {
      lighthouseSummary: result.lighthouseSummary || 'Advanced metrics analysis completed',
      performanceBreakdown: result.performanceBreakdown || {},
      accessibilityInsights: result.accessibilityInsights || 'Accessibility analysis completed',
      bestPracticesAnalysis: result.bestPracticesAnalysis || 'Best practices review completed',
      seoTechnicalSummary: result.seoTechnicalSummary || 'Technical SEO analysis completed',
      mobilePerformanceNotes: result.mobilePerformanceNotes || 'Mobile performance analysis completed',
      optimizationPriorities: result.optimizationPriorities || ['Optimize Core Web Vitals', 'Improve loading speed', 'Enhance user experience'],
      businessImpactAssessment: result.businessImpactAssessment || 'Performance optimization can improve user experience and conversions',
      technicalDebtAnalysis: result.technicalDebtAnalysis || 'Technical debt assessment completed',
      monitoringRecommendations: result.monitoringRecommendations || ['Monitor Core Web Vitals', 'Track page load times', 'Set up performance alerts']
    };
  } catch (error) {
    log.error(`Error generating advanced metrics summary: ${error}`);
    throw error;
  }
}
