/**
 * Enhanced AI analyzer that generates contextual insights based on actual website data
 * Replaces generic score-based logic with AI-generated, website-specific recommendations
 */

import { 
  createProsConsPrompt, 
  createPerformanceImpactPrompt, 
  createLeadInsightsPrompt, 
  createPageSummaryPrompt,
  createPerformanceSEOSummaryPrompt,
  type WebsiteContext 
} from './ai-prompt-templates';
import { extractWebsiteContext } from './data-extraction-utils';
import type { Database } from '../types/supabase';
import { log } from './logger';

type Analysis = Database['public']['Tables']['analyses']['Row'];

// OpenRouter configuration
const OPENROUTER_API_KEY = import.meta.env.OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

const models = {
  'qwen': 'qwen/qwen-2.5-72b-instruct',
  'claude': 'anthropic/claude-3.5-sonnet',
  'gpt4': 'openai/gpt-4o-mini'
};

const activeModel = models['qwen'];

/**
 * Generate AI-powered pros and cons analysis
 */
export async function generateAIProsConsAnalysis(analysis: Analysis): Promise<{
  strengths: Array<{ insight: string; evidence: string; impact: string }>;
  weaknesses: Array<{ insight: string; evidence: string; impact: string; recommendation: string }>;
  overallAssessment: string;
}> {
  try {
    const context = extractWebsiteContext(analysis);
    const prompt = createProsConsPrompt(context);

    log.info(`Generating AI pros/cons analysis for ${context.url}`);

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are an expert conversion rate optimization consultant. Analyze websites based on actual content and provide specific, actionable insights."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 2000,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI pros/cons analysis generated for ${context.url}`);
    
    return {
      strengths: result.strengths || [],
      weaknesses: result.weaknesses || [],
      overallAssessment: result.overallAssessment || 'Analysis completed'
    };
  } catch (error) {
    log.error(`Error generating AI pros/cons analysis: ${error}`);
    throw error;
  }
}

/**
 * Generate AI-powered performance impact analysis
 */
export async function generateAIPerformanceAnalysis(analysis: Analysis): Promise<{
  primaryIssue: {
    title: string;
    description: string;
    evidence: string;
    conversionImpact: string;
  };
  keyFindings: Array<{
    metric: string;
    currentValue: string;
    targetValue: string;
    impact: string;
  }>;
  recommendations: Array<{
    title: string;
    description: string;
    expectedImprovement: string;
    priority: string;
  }>;
  businessImpact: string;
}> {
  try {
    const context = extractWebsiteContext(analysis);
    const prompt = createPerformanceImpactPrompt(context);

    log.info(`Generating AI performance analysis for ${context.url}`);

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are a web performance expert specializing in Core Web Vitals and conversion optimization. Analyze actual performance data to provide specific insights."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 2000,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI performance analysis generated for ${context.url}`);
    
    return {
      primaryIssue: result.primaryIssue || {
        title: 'Performance Analysis Complete',
        description: 'Performance metrics have been analyzed',
        evidence: 'Based on available data',
        conversionImpact: 'Impact assessment completed'
      },
      keyFindings: result.keyFindings || [],
      recommendations: result.recommendations || [],
      businessImpact: result.businessImpact || 'Performance optimization recommendations provided'
    };
  } catch (error) {
    log.error(`Error generating AI performance analysis: ${error}`);
    throw error;
  }
}

/**
 * Generate AI-powered lead insights analysis
 */
export async function generateAILeadInsights(analysis: Analysis): Promise<{
  businessType: string;
  valueProposition: string;
  targetAudience: string;
  leadQuestions: Array<{
    question: string;
    context: string;
    qualification: string;
  }>;
  leadConcerns: Array<{
    concern: string;
    trigger: string;
    resolution: string;
  }>;
  conversionBarriers: Array<{
    barrier: string;
    evidence: string;
    solution: string;
  }>;
  leadScoringFactors: Array<{
    factor: string;
    reasoning: string;
  }>;
}> {
  try {
    const context = extractWebsiteContext(analysis);
    const prompt = createLeadInsightsPrompt(context);

    log.info(`Generating AI lead insights for ${context.url}`);

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are a lead generation and sales psychology expert. Analyze actual website content to identify realistic prospect questions and concerns."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 2500,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI lead insights generated for ${context.url}`);
    
    return {
      businessType: result.businessType || 'Business type identified',
      valueProposition: result.valueProposition || 'Value proposition analyzed',
      targetAudience: result.targetAudience || 'Target audience identified',
      leadQuestions: result.leadQuestions || [],
      leadConcerns: result.leadConcerns || [],
      conversionBarriers: result.conversionBarriers || [],
      leadScoringFactors: result.leadScoringFactors || []
    };
  } catch (error) {
    log.error(`Error generating AI lead insights: ${error}`);
    throw error;
  }
}

/**
 * Generate AI-powered page summary analysis
 */
export async function generateAIPageSummary(analysis: Analysis): Promise<{
  pageOverview: {
    businessType: string;
    mainPurpose: string;
    targetAudience: string;
  };
  valueProposition: {
    primary: string;
    supporting: string[];
    clarity: string;
  };
  heroSectionAnalysis: {
    headline: string;
    effectiveness: string;
    cta: string;
    improvements: string;
  };
  contentQuality: {
    keyTakeaways: string[];
    informationGaps: string[];
    userJourney: string;
  };
  conversionElements: {
    strengths: string[];
    weaknesses: string[];
    recommendations: string[];
  };
}> {
  try {
    const context = extractWebsiteContext(analysis);
    const prompt = createPageSummaryPrompt(context);

    log.info(`Generating AI page summary for ${context.url}`);

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are a UX and content strategy expert. Analyze actual website pages to create comprehensive summaries of visitor experience."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 2500,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI page summary generated for ${context.url}`);
    
    return {
      pageOverview: result.pageOverview || {
        businessType: 'Business type analyzed',
        mainPurpose: 'Page purpose identified',
        targetAudience: 'Target audience determined'
      },
      valueProposition: result.valueProposition || {
        primary: 'Primary value proposition identified',
        supporting: [],
        clarity: 'Value proposition clarity assessed'
      },
      heroSectionAnalysis: result.heroSectionAnalysis || {
        headline: 'Headline analyzed',
        effectiveness: 'Effectiveness assessed',
        cta: 'Call-to-action reviewed',
        improvements: 'Improvement recommendations provided'
      },
      contentQuality: result.contentQuality || {
        keyTakeaways: [],
        informationGaps: [],
        userJourney: 'User journey analyzed'
      },
      conversionElements: result.conversionElements || {
        strengths: [],
        weaknesses: [],
        recommendations: []
      }
    };
  } catch (error) {
    log.error(`Error generating AI page summary: ${error}`);
    throw error;
  }
}

/**
 * Generate AI-powered performance & SEO summary
 */
export async function generateAIPerformanceSEOSummary(analysis: Analysis): Promise<{
  executiveSummary: string;
  performanceFindings: Array<{
    issue: string;
    impact: string;
    solution: string;
    priority: string;
  }>;
  seoFindings: Array<{
    issue: string;
    impact: string;
    solution: string;
    priority: string;
  }>;
  businessImpact: {
    currentState: string;
    potentialGains: string;
    timeline: string;
  };
  actionPlan: Array<{
    phase: string;
    tasks: string[];
    expectedOutcome: string;
    timeframe: string;
  }>;
}> {
  try {
    const context = extractWebsiteContext(analysis);
    const prompt = createPerformanceSEOSummaryPrompt(context);

    log.info(`Generating AI performance & SEO summary for ${context.url}`);

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are a technical SEO and web performance expert. Analyze actual audit data to provide specific, actionable insights."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 2500,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);

    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI performance & SEO summary generated for ${context.url}`);

    return {
      executiveSummary: result.executiveSummary || 'Performance and SEO analysis completed',
      performanceFindings: result.performanceFindings || [],
      seoFindings: result.seoFindings || [],
      businessImpact: result.businessImpact || {
        currentState: 'Current state assessed',
        potentialGains: 'Improvement potential identified',
        timeline: 'Implementation timeline provided'
      },
      actionPlan: result.actionPlan || []
    };
  } catch (error) {
    log.error(`Error generating AI performance & SEO summary: ${error}`);
    throw error;
  }
}
