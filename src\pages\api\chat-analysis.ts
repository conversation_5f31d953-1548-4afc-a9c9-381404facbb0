import type { APIRoute } from 'astro';

export const prerender = false;

const OPENROUTER_API_KEY = import.meta.env.PUBLIC_OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

if (!OPENROUTER_API_KEY) {
  throw new Error('Missing PUBLIC_OPENROUTER_API_KEY environment variable');
}

const models = {
  'qwen': 'qwen/qwen-2.5-72b-instruct:free',
  'claude-sonnet': 'anthropic/claude-3.5-sonnet',
  'gpt-4o': 'openai/gpt-4o',
};

const activeModel = models['qwen'];

export const POST: APIRoute = async ({ request }) => {
  try {
    const { message, analysisData, chatContext } = await request.json();

    if (!message || !analysisData) {
      return new Response(
        JSON.stringify({ error: 'Message and analysis data are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Prepare comprehensive context about the website analysis
    const baseContext = `
You are an expert website optimization consultant specializing in conversion rate optimization, performance, SEO, and user experience. You are having a conversation with a user about their comprehensive website analysis.

WEBSITE ANALYSIS CONTEXT:
Website: ${analysisData.url}
Title: ${analysisData.title || 'Not specified'}
Analysis Date: ${analysisData.createdAt ? new Date(analysisData.createdAt).toLocaleDateString() : 'Recent'}

SCORING OVERVIEW (0-10 scale for conversion, 0-100 for performance/SEO):
• Overall Conversion Score: ${analysisData.score}/10
• Performance Score: ${analysisData.performanceScore || 'Not available'}/100 (Grade: ${analysisData.performanceGrade || 'Not graded'})
• SEO Score: ${analysisData.seoScore || 'Not available'}/100
• Estimated Conversion Rate: ${analysisData.conversionRate || 'Not calculated'}%`;

    // Add enhanced chat context if available
    const enhancedContext = chatContext ? `

ENHANCED ANALYSIS CONTEXT:
Website Summary: ${chatContext.websiteSummary}
Business Context: ${chatContext.businessContext}
Key Metrics: ${JSON.stringify(chatContext.keyMetrics)}
Optimization Opportunities: ${chatContext.optimizationOpportunities.join(', ')}
Technical Context: Available for detailed performance and SEO discussions` : '';

    const performanceContext = `

PERFORMANCE INSIGHTS:
${analysisData.lighthouseData ? `
Core Web Vitals and performance metrics are available from Lighthouse analysis.
Key performance factors affecting user experience and conversion rates.
` : 'Performance data available from analysis.'}

CONVERSION OPTIMIZATION:
Target Audience: ${analysisData.targetAudience || 'General web users'}

Strengths (What's Working Well):
${Array.isArray(analysisData.pros) ? analysisData.pros.map((pro: string) => `• ${pro}`).join('\n') : '• Strong foundation elements identified'}

Areas for Improvement (Conversion Barriers):
${Array.isArray(analysisData.cons) ? analysisData.cons.map((con: string) => `• ${con}`).join('\n') : '• Optimization opportunities identified'}

Strategic Recommendations:
${Array.isArray(analysisData.recommendations) ? analysisData.recommendations.map((rec: string) => `• ${rec}`).join('\n') : '• Tailored improvement strategies available'}

ADAPTIVE STRATEGIES:
${Array.isArray(analysisData.adaptations) ? analysisData.adaptations.map((adapt: string) => `• ${adapt}`).join('\n') : '• Custom optimization approaches recommended'}

CONVERSATION GUIDELINES:
- Provide actionable, data-driven advice based on the specific analysis results
- Prioritize recommendations by impact potential and implementation difficulty
- Reference specific scores and metrics when relevant
- Offer practical next steps and implementation guidance
- Be conversational but professional
- Focus on business impact and ROI
- Consider the relationship between performance, SEO, and conversion optimization

Keep responses focused and actionable (2-4 paragraphs max). Use bullet points for multiple recommendations.
Always tie advice back to the specific analysis data and scores provided.`;

    const contextPrompt = baseContext + enhancedContext + performanceContext;

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: contextPrompt
          },
          {
            role: "user",
            content: message
          }
        ],
        max_tokens: 1000,
        temperature: 0.7,
      })
    });

    const data = await response.json();

    if (data.error) {
      console.error('OpenRouter API Error:', data.error);
      throw new Error(data.error.message || 'An unknown error occurred with the OpenRouter API.');
    }

    if (!data.choices || data.choices.length === 0) {
      console.error('Invalid response from OpenRouter:', data);
      throw new Error('Received an invalid response from the AI.');
    }

    const aiResponse = data.choices[0].message.content || 'I apologize, but I couldn\'t generate a response. Please try again.';

    return new Response(JSON.stringify({ 
      response: aiResponse,
      success: true 
    }), { 
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in chat analysis:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Failed to process chat message',
      response: 'I apologize, but I\'m having trouble processing your request right now. Please try again in a moment.'
    }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
