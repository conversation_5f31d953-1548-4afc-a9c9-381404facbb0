/**
 * AI-Driven Insight Generator
 * Replaces score-based logic with intelligent, contextual analysis
 */

import { loggedFetch, log } from './logger';
import type { Database } from '../types/supabase';
import { supabaseAdmin } from './supabase-client';

type Analysis = Database['public']['Tables']['analyses']['Row'];

const OPENROUTER_API_KEY = import.meta.env.PUBLIC_OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

if (!OPENROUTER_API_KEY) {
  throw new Error('Missing PUBLIC_OPENROUTER_API_KEY environment variable');
}

const activeModel = 'qwen/qwen-2.5-72b-instruct:free';

export interface WebsiteContext {
  url: string;
  htmlContent: string;
  title: string;
  metaDescription?: string;
  headings: string[];
  images: string[];
  businessType: string;
  targetAudience: string;
  valueProposition: string;
  performanceData: any;
  seoData: any;
  lighthouseData: any;
}

export interface AIInsight {
  title: string; // ≤10 words
  description: string;
  evidence: string;
  impactExplanation: string;
  implementationSteps: string;
  businessValue: string;
  priorityScore: number; // 0-100
  confidenceScore: number; // 0-100
  category: string;
  insightType: 'strength' | 'weakness' | 'opportunity' | 'recommendation';
}

export interface LeadInsight {
  questionType: 'qualification' | 'concern' | 'business' | 'objection';
  questionText: string;
  contextExplanation: string;
  qualificationValue: string;
  suggestedResponse: string;
  priorityLevel: number; // 1-5
}

export interface PerformanceInsight {
  impactType: 'critical' | 'opportunity' | 'optimization';
  metricName: string;
  currentValue: number;
  targetValue: number;
  impactDescription: string;
  conversionImpact: string;
  implementationGuide: string;
  expectedImprovement: string;
  effortLevel: 'Low' | 'Medium' | 'High';
  timeline: string;
}

/**
 * Extract comprehensive website context from analysis data
 */
export function extractWebsiteContext(analysis: Analysis): WebsiteContext {
  const lighthouseData = analysis.lighthouse_data ? 
    (typeof analysis.lighthouse_data === 'string' ? 
      JSON.parse(analysis.lighthouse_data) : 
      analysis.lighthouse_data) : null;

  const seoData = analysis.seo_data ? 
    (typeof analysis.seo_data === 'string' ? 
      JSON.parse(analysis.seo_data) : 
      analysis.seo_data) : null;

  const scrapedData = lighthouseData?.scrapedData || {};

  return {
    url: analysis.url || '',
    htmlContent: scrapedData.content || '',
    title: analysis.title || scrapedData.title || '',
    metaDescription: scrapedData.metadata?.description,
    headings: scrapedData.headings || [],
    images: scrapedData.images || [],
    businessType: 'Unknown',
    targetAudience: 'General audience',
    valueProposition: 'Value proposition not identified',
    performanceData: {
      performanceScore: analysis.performance_score,
      lcpScore: analysis.lcp_score,
      fidScore: analysis.fid_score,
      clsScore: analysis.cls_score,
      performanceGrade: analysis.performance_grade
    },
    seoData: seoData,
    lighthouseData: lighthouseData
  };
}

/**
 * Generate AI-powered pros and cons insights
 */
export async function generateAIProsConsInsights(analysis: Analysis): Promise<{
  strengths: AIInsight[];
  weaknesses: AIInsight[];
  overallAssessment: string;
}> {
  const context = extractWebsiteContext(analysis);
  
  const prompt = `You are an expert conversion rate optimization consultant. Analyze this website and provide specific, actionable pros and cons based on the actual content and structure.

WEBSITE CONTEXT:
- URL: ${context.url}
- Title: ${context.title}
- Business Type: ${context.businessType}
- Target Audience: ${context.targetAudience}
- Performance Score: ${context.performanceData.performanceScore || 'Not available'}
- SEO Score: ${analysis.seo_score || 'Not available'}

ACTUAL CONTENT ANALYSIS:
${context.htmlContent.substring(0, 3000)}...

HEADINGS STRUCTURE:
${context.headings.slice(0, 8).join(' | ')}

PERFORMANCE METRICS:
- LCP: ${context.performanceData.lcpScore || 'N/A'}s
- FID: ${context.performanceData.fidScore || 'N/A'}ms  
- CLS: ${context.performanceData.clsScore || 'N/A'}

INSTRUCTIONS:
1. Analyze the ACTUAL website content, not just scores
2. Identify specific strengths based on detected elements (forms, CTAs, content quality, etc.)
3. Identify specific weaknesses based on missing elements or poor implementation
4. Each insight should reference actual page elements or detected issues
5. Provide actionable recommendations with specific implementation steps
6. Focus on conversion optimization impact for this specific business type

Return a JSON object with this structure:
{
  "strengths": [
    {
      "title": "Concise strength header (≤10 words)",
      "description": "Detailed explanation of what works well",
      "evidence": "Specific element or content observed",
      "impactExplanation": "How this helps conversions for this business type",
      "businessValue": "Business impact of maintaining this strength",
      "priorityScore": 85,
      "confidenceScore": 90,
      "category": "conversion|performance|seo|content|trust|ux"
    }
  ],
  "weaknesses": [
    {
      "title": "Concise weakness header (≤10 words)",
      "description": "Detailed explanation of the issue",
      "evidence": "Specific missing element or poor implementation observed",
      "impactExplanation": "How this hurts conversions for this business type",
      "implementationSteps": "Step-by-step instructions to fix this issue",
      "businessValue": "Business impact of fixing this weakness",
      "priorityScore": 75,
      "confidenceScore": 85,
      "category": "conversion|performance|seo|content|trust|ux"
    }
  ],
  "overallAssessment": "Brief summary of the page's conversion potential based on actual analysis"
}

Limit to 6 strengths and 6 weaknesses maximum. Be specific and reference actual page elements.`;

  try {
    log.info(`Generating AI pros/cons insights for ${context.url}`);
    
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are an expert conversion rate optimization consultant. Analyze websites based on actual content and provide specific, actionable insights."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 3000,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI pros/cons insights generated for ${context.url}`);
    
    return {
      strengths: result.strengths || [],
      weaknesses: result.weaknesses || [],
      overallAssessment: result.overallAssessment || 'Analysis completed based on website content'
    };
  } catch (error) {
    log.error(`Error generating AI pros/cons insights: ${error}`);
    throw error;
  }
}

/**
 * Generate AI-powered lead qualification insights
 */
export async function generateAILeadInsights(analysis: Analysis): Promise<{
  businessType: string;
  valueProposition: string;
  targetAudience: string;
  leadQuestions: LeadInsight[];
  businessImpact: string[];
  conversionBarriers: Array<{
    barrier: string;
    evidence: string;
    solution: string;
  }>;
}> {
  const context = extractWebsiteContext(analysis);
  
  const prompt = `You are a lead generation and sales psychology expert. Analyze this actual website content to identify realistic questions, concerns, and objections that prospects would have.

WEBSITE CONTEXT:
- URL: ${context.url}
- Title: ${context.title}
- Business Type: ${context.businessType}
- Target Audience: ${context.targetAudience}

ACTUAL PAGE CONTENT:
${context.htmlContent.substring(0, 4000)}...

HEADINGS STRUCTURE:
${context.headings.join(' | ')}

DETECTED ELEMENTS:
- Images: ${context.images.length} found
- Business Type: ${context.businessType}
- Forms: ${context.htmlContent.includes('<form') ? 'Contact/signup forms detected' : 'No forms detected'}
- Pricing: ${context.htmlContent.toLowerCase().includes('price') || context.htmlContent.toLowerCase().includes('$') ? 'Pricing information present' : 'No pricing information visible'}

INSTRUCTIONS:
1. Analyze the ACTUAL content, value proposition, and messaging
2. Identify the specific business type and target audience from content analysis
3. Generate realistic questions prospects would have after reading THIS specific page
4. Identify concerns based on what's missing or unclear in the content
5. Consider the user journey and what information gaps exist
6. Focus on lead qualification and sales enablement

Return a JSON object with this structure:
{
  "businessType": "Specific business type identified from content analysis",
  "valueProposition": "Main value proposition extracted from the page",
  "targetAudience": "Specific target audience identified from content and messaging",
  "leadQuestions": [
    {
      "questionType": "qualification|concern|business|objection",
      "questionText": "Specific question a prospect would ask after visiting this page",
      "contextExplanation": "Why this question arises from the content",
      "qualificationValue": "How this question helps qualify the lead",
      "suggestedResponse": "How to address this question in sales conversations",
      "priorityLevel": 4
    }
  ],
  "businessImpact": [
    "Specific business impact or opportunity identified from the analysis"
  ],
  "conversionBarriers": [
    {
      "barrier": "Specific element preventing conversion",
      "evidence": "What you observed in the content",
      "solution": "Specific recommendation to remove this barrier"
    }
  ]
}

Generate 8-12 lead questions across different types. Be specific to this website's actual content and business model.`;

  try {
    log.info(`Generating AI lead insights for ${context.url}`);
    
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are a lead generation and sales psychology expert. Analyze actual website content to identify realistic prospect questions and concerns."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 3000,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI lead insights generated for ${context.url}`);
    
    return {
      businessType: result.businessType || context.businessType,
      valueProposition: result.valueProposition || context.valueProposition,
      targetAudience: result.targetAudience || context.targetAudience,
      leadQuestions: result.leadQuestions || [],
      businessImpact: result.businessImpact || [],
      conversionBarriers: result.conversionBarriers || []
    };
  } catch (error) {
    log.error(`Error generating AI lead insights: ${error}`);
    throw error;
  }
}

/**
 * Generate AI-powered performance impact analysis
 */
export async function generateAIPerformanceInsights(analysis: Analysis): Promise<{
  primaryIssue: {
    title: string;
    description: string;
    evidence: string;
    conversionImpact: string;
  };
  performanceInsights: PerformanceInsight[];
  businessImpact: string;
  recommendations: Array<{
    title: string;
    description: string;
    expectedImprovement: string;
    priority: 'High' | 'Medium' | 'Low';
  }>;
}> {
  const context = extractWebsiteContext(analysis);
  
  const prompt = `You are a web performance expert specializing in Core Web Vitals and conversion optimization. Analyze the actual performance data to provide specific, actionable insights.

WEBSITE CONTEXT:
- URL: ${context.url}
- Business Type: ${context.businessType}
- Target Audience: ${context.targetAudience}

ACTUAL PERFORMANCE DATA:
- Performance Score: ${context.performanceData.performanceScore || 'Not available'}/100
- LCP: ${context.performanceData.lcpScore || 'N/A'}s
- FID: ${context.performanceData.fidScore || 'N/A'}ms
- CLS: ${context.performanceData.clsScore || 'N/A'}
- Grade: ${context.performanceData.performanceGrade || 'Not graded'}

LIGHTHOUSE AUDIT RESULTS:
${context.lighthouseData ? JSON.stringify(context.lighthouseData, null, 2).substring(0, 2000) : 'Lighthouse data not available'}

HTML CONTENT (for context):
${context.htmlContent.substring(0, 2000)}...

INSTRUCTIONS:
1. Analyze the ACTUAL Lighthouse audit results and performance metrics
2. Identify specific performance bottlenecks from the audit data
3. Calculate realistic conversion impact based on actual metrics for this business type
4. Provide specific, implementable recommendations
5. Reference actual detected issues (large images, render-blocking resources, etc.)

Return a JSON object with this structure:
{
  "primaryIssue": {
    "title": "Concise header (≤10 words) describing the main performance problem",
    "description": "Detailed explanation of the specific issue found in the audit",
    "evidence": "Specific metrics or audit findings that support this",
    "conversionImpact": "Realistic estimate of conversion impact with reasoning for this business type"
  },
  "performanceInsights": [
    {
      "impactType": "critical|opportunity|optimization",
      "metricName": "Specific Core Web Vital or performance metric",
      "currentValue": 2.8,
      "targetValue": 2.5,
      "impactDescription": "How this affects user experience and conversions",
      "conversionImpact": "Specific conversion impact for this business type",
      "implementationGuide": "Step-by-step instructions to fix",
      "expectedImprovement": "Realistic performance gain",
      "effortLevel": "Low|Medium|High",
      "timeline": "Expected implementation time"
    }
  ],
  "businessImpact": "Overall assessment of how performance affects this specific website's business goals",
  "recommendations": [
    {
      "title": "Specific, actionable recommendation header (≤10 words)",
      "description": "Detailed implementation steps",
      "expectedImprovement": "Realistic performance gain",
      "priority": "High|Medium|Low"
    }
  ]
}

Focus on actionable insights based on actual audit data, not generic performance advice.`;

  try {
    log.info(`Generating AI performance insights for ${context.url}`);
    
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are a web performance expert specializing in Core Web Vitals and conversion optimization. Analyze actual performance data to provide specific insights."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 3000,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI performance insights generated for ${context.url}`);
    
    return {
      primaryIssue: result.primaryIssue || {
        title: 'Performance Analysis Complete',
        description: 'Performance metrics have been analyzed',
        evidence: 'Based on available data',
        conversionImpact: 'Impact assessment completed'
      },
      performanceInsights: result.performanceInsights || [],
      businessImpact: result.businessImpact || 'Performance optimization recommendations provided',
      recommendations: result.recommendations || []
    };
  } catch (error) {
    log.error(`Error generating AI performance insights: ${error}`);
    throw error;
  }
}

/**
 * Generate AI-powered page summary for lead insights
 */
export async function generateAIPageSummary(analysis: Analysis): Promise<{
  pagePurpose: string;
  keyTakeaways: string[];
  heroEffectiveness: string;
  valuePropositionClarity: string;
  leadCaptureAssessment: string;
  userJourneyAnalysis: string;
}> {
  const context = extractWebsiteContext(analysis);
  
  const prompt = `You are a UX and content strategy expert. Analyze this actual website page to create a comprehensive summary focused on lead generation and conversion effectiveness.

WEBSITE CONTEXT:
- URL: ${context.url}
- Title: ${context.title}
- Business Type: ${context.businessType}
- Target Audience: ${context.targetAudience}

ACTUAL PAGE CONTENT:
${context.htmlContent.substring(0, 4000)}...

CONTENT STRUCTURE:
- Main Headings: ${context.headings.slice(0, 8).join(' | ')}
- Images: ${context.images.length} detected
- Forms: ${context.htmlContent.includes('<form') ? 'Contact/signup forms detected' : 'No forms detected'}

INSTRUCTIONS:
1. Analyze the ACTUAL page content and structure
2. Assess how effectively the page communicates value to prospects
3. Evaluate the hero section's hook and clarity
4. Identify what leads would understand and remember
5. Assess lead capture mechanisms and user journey

Return a JSON object with this structure:
{
  "pagePurpose": "Clear statement of what this page is designed to accomplish",
  "keyTakeaways": [
    "What visitors would remember after reading this page",
    "Main benefits or value propositions they'd understand",
    "Key differentiators that would stick with them"
  ],
  "heroEffectiveness": "Assessment of how well the hero section hooks visitors and communicates value",
  "valuePropositionClarity": "How clearly the page communicates its unique value proposition",
  "leadCaptureAssessment": "Analysis of lead capture mechanisms and their effectiveness",
  "userJourneyAnalysis": "How well the content guides users toward conversion"
}

Base all analysis on actual page content, not assumptions.`;

  try {
    log.info(`Generating AI page summary for ${context.url}`);
    
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are a UX and content strategy expert. Analyze actual website pages to create comprehensive summaries focused on lead generation effectiveness."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 2000,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI page summary generated for ${context.url}`);
    
    return {
      pagePurpose: result.pagePurpose || 'Page purpose analysis completed',
      keyTakeaways: result.keyTakeaways || [],
      heroEffectiveness: result.heroEffectiveness || 'Hero section analysis completed',
      valuePropositionClarity: result.valuePropositionClarity || 'Value proposition analysis completed',
      leadCaptureAssessment: result.leadCaptureAssessment || 'Lead capture analysis completed',
      userJourneyAnalysis: result.userJourneyAnalysis || 'User journey analysis completed'
    };
  } catch (error) {
    log.error(`Error generating AI page summary: ${error}`);
    throw error;
  }
}

/**
 * Generate AI-powered performance & SEO summary
 */
export async function generateAIPerformanceSEOSummary(analysis: Analysis): Promise<{
  executiveSummary: string;
  keyFindings: string[];
  businessImpact: string;
  actionPlan: Array<{
    phase: string;
    title: string;
    description: string;
    timeline: string;
    expectedImpact: string;
  }>;
}> {
  const context = extractWebsiteContext(analysis);
  
  const prompt = `You are a technical SEO and web performance expert. Analyze the actual audit data to provide specific, actionable insights for business impact.

WEBSITE CONTEXT:
- URL: ${context.url}
- Business Type: ${context.businessType}
- Target Audience: ${context.targetAudience}

PERFORMANCE DATA:
- Performance Score: ${context.performanceData.performanceScore || 'Not available'}/100
- LCP: ${context.performanceData.lcpScore || 'N/A'}s
- FID: ${context.performanceData.fidScore || 'N/A'}ms
- CLS: ${context.performanceData.clsScore || 'N/A'}

SEO DATA:
${context.seoData ? JSON.stringify(context.seoData, null, 2).substring(0, 1500) : 'SEO data not available'}

INSTRUCTIONS:
1. Analyze ACTUAL audit results and detected issues
2. Identify specific technical problems from the data
3. Provide actionable recommendations with implementation details
4. Calculate realistic business impact based on actual metrics for this business type
5. Create a phased action plan with realistic timelines

Return a JSON object with this structure:
{
  "executiveSummary": "High-level assessment based on actual audit results for this business type",
  "keyFindings": [
    "Most important technical findings that affect this business",
    "Specific performance or SEO issues detected",
    "Opportunities for improvement with highest business impact"
  ],
  "businessImpact": "How performance and SEO issues specifically affect this business's goals and revenue",
  "actionPlan": [
    {
      "phase": "Immediate (0-2 weeks)",
      "title": "Quick technical wins",
      "description": "Specific actions to take immediately",
      "timeline": "1-2 weeks",
      "expectedImpact": "Realistic improvement estimate"
    },
    {
      "phase": "Short-term (1-3 months)",
      "title": "Strategic improvements",
      "description": "Medium-effort optimizations",
      "timeline": "4-12 weeks", 
      "expectedImpact": "Realistic improvement estimate"
    },
    {
      "phase": "Long-term (3-6 months)",
      "title": "Advanced optimizations",
      "description": "Complex technical enhancements",
      "timeline": "3-6 months",
      "expectedImpact": "Realistic improvement estimate"
    }
  ]
}

Focus on specific, implementable recommendations based on actual audit findings for this business type.`;

  try {
    log.info(`Generating AI performance & SEO summary for ${context.url}`);
    
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          {
            role: "system",
            content: "You are a technical SEO and web performance expert. Analyze actual audit data to provide specific, actionable insights."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 2500,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);

    const result = JSON.parse(data.choices[0]?.message?.content || '{}');
    log.info(`AI performance & SEO summary generated for ${context.url}`);

    return {
      executiveSummary: result.executiveSummary || 'Performance and SEO analysis completed',
      keyFindings: result.keyFindings || [],
      businessImpact: result.businessImpact || 'Performance and SEO improvements can impact business goals',
      actionPlan: result.actionPlan || []
    };
  } catch (error) {
    log.error(`Error generating AI performance & SEO summary: ${error}`);
    throw error;
  }
}

/**
 * Store AI insights in database
 */
export async function storeAIInsights(
  analysisId: string,
  insights: AIInsight[]
): Promise<void> {
  try {
    log.info(`Storing ${insights.length} AI insights for analysis ${analysisId}`);

    // Get category IDs
    const { data: categories } = await supabaseAdmin
      .from('insight_categories')
      .select('id, name');

    const categoryMap = new Map(categories?.map(cat => [cat.name, cat.id]) || []);

    // Prepare insights for insertion
    const insightsToInsert = insights.map(insight => ({
      analysis_id: analysisId,
      category_id: categoryMap.get(insight.category) || categoryMap.get('conversion'),
      insight_type: insight.insightType,
      title: insight.title,
      description: insight.description,
      evidence: insight.evidence,
      impact_explanation: insight.impactExplanation,
      implementation_steps: insight.implementationSteps,
      business_value: insight.businessValue,
      priority_score: insight.priorityScore,
      confidence_score: insight.confidenceScore
    }));

    const { error } = await supabaseAdmin
      .from('ai_insights')
      .insert(insightsToInsert);

    if (error) throw error;

    log.info(`AI insights stored successfully for analysis ${analysisId}`);
  } catch (error) {
    log.error(`Error storing AI insights: ${error}`);
    throw error;
  }
}

/**
 * Store lead insights in database
 */
export async function storeLeadInsights(
  analysisId: string,
  leadInsights: LeadInsight[]
): Promise<void> {
  try {
    log.info(`Storing ${leadInsights.length} lead insights for analysis ${analysisId}`);

    const insightsToInsert = leadInsights.map(insight => ({
      analysis_id: analysisId,
      question_type: insight.questionType,
      question_text: insight.questionText,
      context_explanation: insight.contextExplanation,
      qualification_value: insight.qualificationValue,
      suggested_response: insight.suggestedResponse,
      priority_level: insight.priorityLevel
    }));

    const { error } = await supabaseAdmin
      .from('lead_qualification_insights')
      .insert(insightsToInsert);

    if (error) throw error;

    log.info(`Lead insights stored successfully for analysis ${analysisId}`);
  } catch (error) {
    log.error(`Error storing lead insights: ${error}`);
    throw error;
  }
}

/**
 * Store performance insights in database
 */
export async function storePerformanceInsights(
  analysisId: string,
  performanceInsights: PerformanceInsight[]
): Promise<void> {
  try {
    log.info(`Storing ${performanceInsights.length} performance insights for analysis ${analysisId}`);

    const insightsToInsert = performanceInsights.map(insight => ({
      analysis_id: analysisId,
      impact_type: insight.impactType,
      metric_name: insight.metricName,
      current_value: insight.currentValue,
      target_value: insight.targetValue,
      impact_description: insight.impactDescription,
      conversion_impact: insight.conversionImpact,
      implementation_guide: insight.implementationGuide,
      expected_improvement: insight.expectedImprovement,
      effort_level: insight.effortLevel,
      timeline: insight.timeline
    }));

    const { error } = await supabaseAdmin
      .from('performance_impact_insights')
      .insert(insightsToInsert);

    if (error) throw error;

    log.info(`Performance insights stored successfully for analysis ${analysisId}`);
  } catch (error) {
    log.error(`Error storing performance insights: ${error}`);
    throw error;
  }
}