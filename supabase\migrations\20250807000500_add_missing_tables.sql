CREATE TABLE IF NOT EXISTS insight_categories (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    icon TEXT,
    color TEXT
);

CREATE TABLE IF NOT EXISTS ai_insights (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    analysis_id uuid REFERENCES analyses(id) ON DELETE CASCADE,
    category_id uuid REFERENCES insight_categories(id),
    insight_type TEXT,
    title TEXT,
    description TEXT,
    evidence TEXT,
    impact_explanation TEXT,
    implementation_steps TEXT,
    business_value TEXT,
    priority_score INT,
    confidence_score INT,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS lead_qualification_insights (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    analysis_id uuid REFERENCES analyses(id) ON DELETE CASCADE,
    question_type TEXT,
    question_text TEXT,
    context_explanation TEXT,
    qualification_value TEXT,
    suggested_response TEXT,
    priority_level INT,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS performance_impact_insights (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    analysis_id uuid REFERENCES analyses(id) ON DELETE CASCADE,
    impact_type TEXT,
    metric_name TEXT,
    current_value NUMERIC,
    target_value NUMERIC,
    impact_description TEXT,
    conversion_impact TEXT,
    implementation_guide TEXT,
    expected_improvement TEXT,
    effort_level TEXT,
    timeline TEXT,
    created_at timestamptz DEFAULT now()
);

INSERT INTO insight_categories (name, description, icon, color) VALUES
('conversion', 'Insights related to improving conversion rates', 'zap', 'blue'),
('performance', 'Insights related to website speed and performance', 'rocket', 'green'),
('seo', 'Insights related to search engine optimization', 'search', 'purple'),
('content', 'Insights related to website content and copy', 'file-text', 'orange'),
('trust', 'Insights related to building trust and credibility', 'shield', 'teal'),
('ux', 'Insights related to user experience and design', 'figma', 'pink');