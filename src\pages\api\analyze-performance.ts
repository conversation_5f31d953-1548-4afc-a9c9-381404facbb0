// API endpoint for running Lighthouse performance analysis
import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { runLighthouseAnalysis, captureScreenshot } from '../../lib/lighthouse-analyzer';
import { log } from '../../lib/logger';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const { url, analysisId } = await request.json();

    log.info(`Received performance analysis request for URL: ${url} with analysisId: ${analysisId}`);

    if (!url || !analysisId) {
      log.warn('Missing URL or analysisId in performance analysis request');
      return new Response(JSON.stringify({ error: 'Missing required parameters' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Normalize URL
    const normalizedUrl = url.startsWith('http') ? url : `https://${url}`;
    log.debug(`Normalized URL: ${normalizedUrl}`);

    // Run Lighthouse analysis
    const lighthouseMetrics = await runLighthouseAnalysis(normalizedUrl);

    // Capture screenshot
    let screenshotUrl = null;
    try {
      log.info('[API] Attempting to capture screenshot...');
      const screenshot = await captureScreenshot(normalizedUrl);
      log.info('[API] Screenshot captured successfully.');

      // Upload screenshot to Supabase Storage
      const screenshotPath = `public/${analysisId}.png`;
      log.info(`[API] Uploading screenshot to Supabase at ${screenshotPath}...`);
      const { error: uploadError } = await supabaseAdmin.storage
        .from('screenshots')
        .upload(screenshotPath, screenshot, {
          contentType: 'image/png',
          upsert: true,
        });

      if (uploadError) {
        log.error(`[API] Screenshot upload failed: ${uploadError.message}`);
        throw new Error(`Screenshot upload failed: ${uploadError.message}`);
      }
      log.info('[API] Screenshot uploaded successfully.');

      // Get public URL for the uploaded screenshot
      log.info('[API] Getting public URL for screenshot...');
      const { data: publicUrlData } = supabaseAdmin.storage
        .from('screenshots')
        .getPublicUrl(screenshotPath);

      if (!publicUrlData) {
        log.error('[API] Failed to get public URL for screenshot');
        throw new Error('Failed to get public URL for screenshot');
      }
      log.info(`[API] Public URL: ${publicUrlData.publicUrl}`);

      screenshotUrl = publicUrlData.publicUrl;

    } catch (screenshotError) {
      log.warn(`[API] Screenshot capture failed: ${screenshotError instanceof Error ? screenshotError.message : screenshotError}`);
      log.info('[API] Trying fallback screenshot mechanism...');
      try {
        const fallbackUrl = `https://s.wordpress.com/mshots/v1/${encodeURIComponent(normalizedUrl)}?w=1280&h=800`;
        log.info(`[API] Fallback screenshot URL: ${fallbackUrl}`);
        const response = await fetch(fallbackUrl);
        if(response.ok){
          const imageBuffer = await response.arrayBuffer();
          const screenshotPath = `public/${analysisId}.png`;
          log.info(`[API] Uploading fallback screenshot to Supabase at ${screenshotPath}...`);
          const { error: uploadError } = await supabaseAdmin.storage
            .from('screenshots')
            .upload(screenshotPath, imageBuffer, {
              contentType: 'image/png',
              upsert: true,
            });

          if (uploadError) {
            log.error(`[API] Fallback screenshot upload failed: ${uploadError.message}`);
            throw new Error(`Fallback screenshot upload failed: ${uploadError.message}`);
          }

          const { data: publicUrlData } = supabaseAdmin.storage
            .from('screenshots')
            .getPublicUrl(screenshotPath);
          screenshotUrl = publicUrlData.publicUrl;
          log.info('[API] Fallback screenshot uploaded successfully.');
        } else {
          log.warn('[API] Fallback screenshot service failed.');
        }
      } catch (fallbackError) {
        log.error(`[API] Fallback screenshot mechanism failed: ${fallbackError instanceof Error ? fallbackError.message : fallbackError}`);
      }
    }

    // Store results in database
    log.debug('Storing Lighthouse results in database...');
    const { data: existingData, error: fetchError } = await supabaseAdmin
      .from('analyses')
      .select('lighthouse_data')
      .eq('id', analysisId)
      .single();

    if (fetchError) {
      log.error(`Failed to fetch existing analysis data: ${fetchError.message}`);
      throw new Error(`Failed to fetch existing analysis data: ${fetchError.message}`);
    }

    const newLighthouseData = {
      performanceScore: lighthouseMetrics.performanceScore,
      accessibilityScore: lighthouseMetrics.accessibilityScore,
      bestPracticesScore: lighthouseMetrics.bestPracticesScore,
      seoScore: lighthouseMetrics.seoScore,
      coreWebVitals: lighthouseMetrics.coreWebVitals,
      recommendations: lighthouseMetrics.recommendations
    };

    const mergedLighthouseData = {
      ...existingData?.lighthouse_data,
      ...newLighthouseData
    };

    const updateData: any = {
      performance_score: lighthouseMetrics.performanceScore,
      performance_grade: lighthouseMetrics.grade,
      lcp_score: lighthouseMetrics.coreWebVitals.lcp,
      fid_score: lighthouseMetrics.coreWebVitals.fid,
      cls_score: lighthouseMetrics.coreWebVitals.cls,
      lighthouse_data: mergedLighthouseData
    };

    if (screenshotUrl) {
      updateData.screenshot_url = screenshotUrl;
    }

    const { error: updateError } = await supabaseAdmin
      .from('analyses')
      .update(updateData)
      .eq('id', analysisId);

    if (updateError) {
      log.error(`Database update error: ${updateError.message}`);
    }
    log.info('Lighthouse results stored in database.');

    return new Response(JSON.stringify({
      success: true,
      performanceScore: lighthouseMetrics.performanceScore,
      accessibilityScore: lighthouseMetrics.accessibilityScore,
      bestPracticesScore: lighthouseMetrics.bestPracticesScore,
      seoScore: lighthouseMetrics.seoScore,
      coreWebVitals: lighthouseMetrics.coreWebVitals,
      grade: lighthouseMetrics.grade,
      conversionImpact: lighthouseMetrics.conversionImpact,
      recommendations: lighthouseMetrics.recommendations
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`Performance analysis error: ${error instanceof Error ? error.message : error}`);

    return new Response(JSON.stringify({
      error: 'Analysis failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
