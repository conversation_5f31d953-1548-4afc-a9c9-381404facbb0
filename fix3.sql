-- Restore ConvertIQ Database to Checkpoint 1 State
-- This script removes sequential AI generation features and restores the original schema

-- Drop sequential AI generation tables and columns
DROP TABLE IF EXISTS public.analysis_generation_progress CASCADE;
DROP TABLE IF EXISTS public.ai_chat_context CASCADE;
DROP TABLE IF EXISTS public.ai_seo_insights CASCADE;
DROP TABLE IF EXISTS public.advanced_metrics_summary CASCADE;

-- Remove sequential AI generation columns from analyses table
ALTER TABLE public.analyses
DROP COLUMN IF EXISTS generation_status,
DROP COLUMN IF EXISTS generation_started_at,
DROP COLUMN IF EXISTS generation_completed_at,
DROP COLUMN IF EXISTS current_generation_step,
DROP COLUMN IF EXISTS total_generation_steps,
DROP COLUMN IF EXISTS completed_generation_steps,
DROP COLUMN IF EXISTS generation_error,
DROP COLUMN IF EXISTS all_content_generated,
DROP COLUMN IF EXISTS pros_cons_generated,
DROP COLUMN IF EXISTS lead_insights_generated,
DROP COLUMN IF EXISTS performance_insights_generated,
DROP COLUMN IF EXISTS seo_insights_generated,
DROP COLUMN IF EXISTS chat_context_generated,
DROP COLUMN IF EXISTS page_summary_enhanced,
DROP COLUMN IF EXISTS overall_grade,
DROP COLUMN IF EXISTS overall_score,
DROP COLUMN IF EXISTS conversion_rate_score,
DROP COLUMN IF EXISTS user_experience_score,
DROP COLUMN IF EXISTS mobile_score,
DROP COLUMN IF EXISTS accessibility_score,
DROP COLUMN IF EXISTS best_practices_score,
DROP COLUMN IF EXISTS lighthouse_data_updated_at,
DROP COLUMN IF EXISTS scores_calculated_at;

-- Remove AI-specific columns from ai_insights table
ALTER TABLE public.ai_insights
DROP COLUMN IF EXISTS insight_category,
DROP COLUMN IF EXISTS confidence_score,
DROP COLUMN IF EXISTS data_source,
DROP COLUMN IF EXISTS validation_status,
DROP COLUMN IF EXISTS user_feedback,
DROP COLUMN IF EXISTS implementation_complexity;

-- Remove AI-specific columns from ai_page_summaries table
ALTER TABLE public.ai_page_summaries
DROP COLUMN IF EXISTS business_impact_summary,
DROP COLUMN IF EXISTS conversion_assessment,
DROP COLUMN IF EXISTS trust_signals_analysis,
DROP COLUMN IF EXISTS mobile_experience_notes,
DROP COLUMN IF EXISTS competitive_advantages,
DROP COLUMN IF EXISTS improvement_priorities,
DROP COLUMN IF EXISTS business_model_insights,
DROP COLUMN IF EXISTS target_audience_analysis,
DROP COLUMN IF EXISTS content_strategy_notes;

-- Remove AI-specific columns from lead_qualification_insights table
ALTER TABLE public.lead_qualification_insights
DROP COLUMN IF EXISTS business_impact_score,
DROP COLUMN IF EXISTS conversion_relevance,
DROP COLUMN IF EXISTS follow_up_questions,
DROP COLUMN IF EXISTS objection_handling,
DROP COLUMN IF EXISTS qualification_criteria,
DROP COLUMN IF EXISTS lead_scoring_weight;

-- Drop performance_impact_insights table (this was added in sequential AI system)
DROP TABLE IF EXISTS public.performance_impact_insights CASCADE;

-- Drop database functions related to sequential AI generation
DROP FUNCTION IF EXISTS public.initialize_generation_progress(uuid);
DROP FUNCTION IF EXISTS public.update_generation_progress(uuid, text, text, text);
DROP FUNCTION IF EXISTS public.get_generation_progress(uuid);

-- Restore original analyses table structure (checkpoint 1 state)
-- Keep only the core columns that existed before sequential AI generation
ALTER TABLE public.analyses
ADD COLUMN IF NOT EXISTS conversion_score_display numeric DEFAULT score;

-- Ensure the analyses table has the correct constraints for checkpoint 1
ALTER TABLE public.analyses
ALTER COLUMN score SET NOT NULL,
ALTER COLUMN conversion_rate SET NOT NULL,
ALTER COLUMN pros SET NOT NULL,
ALTER COLUMN cons SET NOT NULL,
ALTER COLUMN recommendations SET NOT NULL,
ALTER COLUMN target_audience SET NOT NULL,
ALTER COLUMN adaptations SET NOT NULL;

-- Update any existing analyses to have proper conversion_score_display
UPDATE public.analyses
SET conversion_score_display = score
WHERE conversion_score_display IS NULL;

-- Clean up any orphaned data
DELETE FROM public.ai_insights WHERE analysis_id NOT IN (SELECT id FROM public.analyses);
DELETE FROM public.ai_page_summaries WHERE analysis_id NOT IN (SELECT id FROM public.analyses);
DELETE FROM public.lead_qualification_insights WHERE analysis_id NOT IN (SELECT id FROM public.analyses);
DELETE FROM public.performance_metrics WHERE analysis_id NOT IN (SELECT id FROM public.analyses);
DELETE FROM public.seo_issues WHERE analysis_id NOT IN (SELECT id FROM public.analyses);
DELETE FROM public.suggestions WHERE analysis_id NOT IN (SELECT id FROM public.analyses);
DELETE FROM public.messages WHERE analysis_id NOT IN (SELECT id FROM public.analyses);

-- Restore RLS policies to checkpoint 1 state
-- Drop any sequential AI generation related policies
DROP POLICY IF EXISTS "Users can view their own analysis progress" ON public.analysis_generation_progress;
DROP POLICY IF EXISTS "Users can update their own analysis progress" ON public.analysis_generation_progress;
DROP POLICY IF EXISTS "Users can view their own AI chat context" ON public.ai_chat_context;
DROP POLICY IF EXISTS "Users can view their own AI SEO insights" ON public.ai_seo_insights;
DROP POLICY IF EXISTS "Users can view their own advanced metrics summary" ON public.advanced_metrics_summary;
DROP POLICY IF EXISTS "Users can view their own performance impact insights" ON public.performance_impact_insights;

-- Ensure core RLS policies are in place for checkpoint 1
DO $$
BEGIN
    -- Enable RLS on core tables
    ALTER TABLE public.analyses ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.ai_insights ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.ai_page_summaries ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.lead_qualification_insights ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.performance_metrics ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.seo_issues ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.suggestions ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.suggestion_categories ENABLE ROW LEVEL SECURITY;

    -- Create basic RLS policies for checkpoint 1
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'analyses' AND policyname = 'Users can view their own analyses') THEN
        CREATE POLICY "Users can view their own analyses" ON public.analyses FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'analyses' AND policyname = 'Users can insert their own analyses') THEN
        CREATE POLICY "Users can insert their own analyses" ON public.analyses FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'analyses' AND policyname = 'Users can update their own analyses') THEN
        CREATE POLICY "Users can update their own analyses" ON public.analyses FOR UPDATE USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'profiles' AND policyname = 'Users can view their own profile') THEN
        CREATE POLICY "Users can view their own profile" ON public.profiles FOR SELECT USING (auth.uid() = id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'profiles' AND policyname = 'Users can update their own profile') THEN
        CREATE POLICY "Users can update their own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        -- Continue if policies already exist
        NULL;
END $$;

-- Reset any sequences or indexes that might have been affected
REINDEX TABLE public.analyses;
REINDEX TABLE public.ai_insights;
REINDEX TABLE public.ai_page_summaries;
REINDEX TABLE public.lead_qualification_insights;

-- Vacuum and analyze tables for optimal performance
VACUUM ANALYZE public.analyses;
VACUUM ANALYZE public.ai_insights;
VACUUM ANALYZE public.ai_page_summaries;
VACUUM ANALYZE public.lead_qualification_insights;
VACUUM ANALYZE public.performance_metrics;
VACUUM ANALYZE public.seo_issues;
VACUUM ANALYZE public.suggestions;
VACUUM ANALYZE public.messages;
VACUUM ANALYZE public.profiles;

-- Final verification: Ensure all tables are in checkpoint 1 state
SELECT 'Checkpoint 1 restoration completed successfully' as status;


