<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../types/supabase';
import { 
  Lightbulb, 
  AlertCircle, 
  TrendingUp, 
  Clock,
  Info
} from 'lucide-vue-next';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

type Suggestion = Database['public']['Tables']['suggestions']['Row'];

const suggestions = ref<Suggestion[]>([]);
const loading = ref(true);

onMounted(async () => {
  await loadSuggestions();
});

const loadSuggestions = async () => {
  try {
    const { data, error } = await supabase
      .from('suggestions')
      .select('*')
      .eq('analysis_id', props.analysis.id)
      .order('priority', { ascending: true });

    if (error) throw error;
    suggestions.value = data || [];
  } catch (error) {
    console.error('Error loading suggestions:', error);
  } finally {
    loading.value = false;
  }
};

const groupedSuggestions = computed(() => {
  const groups: Record<string, Suggestion[]> = {};
  suggestions.value.forEach(suggestion => {
    if (!groups[suggestion.category]) {
      groups[suggestion.category] = [];
    }
    groups[suggestion.category].push(suggestion);
  });
  return groups;
});

const getImpactColor = (impact: string) => {
  switch (impact.toLowerCase()) {
    case 'high':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'low':
      return 'bg-green-100 text-green-800 border-green-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getEffortColor = (effort: string) => {
  switch (effort.toLowerCase()) {
    case 'high':
      return 'text-red-600';
    case 'medium':
      return 'text-yellow-600';
    case 'low':
      return 'text-green-600';
    default:
      return 'text-gray-600';
  }
};

const getImpactIcon = (impact: string) => {
  switch (impact.toLowerCase()) {
    case 'high':
      return AlertCircle;
    case 'medium':
      return TrendingUp;
    case 'low':
      return Info;
    default:
      return Info;
  }
};

const getCategoryIcon = (category: string) => {
  switch (category.toLowerCase()) {
    case 'conversion':
      return TrendingUp;
    case 'performance':
      return Clock;
    case 'seo':
      return Lightbulb;
    default:
      return Lightbulb;
  }
};
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <div class="flex items-center mb-2">
          <Lightbulb class="w-5 h-5 text-gray-400 mr-2" />
          <h2 class="text-lg font-semibold text-gray-900">AI Recommendations</h2>
        </div>
        <p class="text-gray-600">Actionable insights to improve your conversion rate</p>
      </div>
      <div class="text-sm text-gray-500">
        {{ suggestions.length }} suggestions found
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="text-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p class="text-gray-500">Loading suggestions...</p>
    </div>

    <!-- Empty State -->
    <div v-else-if="suggestions.length === 0" class="text-center py-12">
      <Lightbulb class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">No suggestions yet</h3>
      <p class="text-gray-500">Analysis is still processing or no suggestions were generated.</p>
    </div>

    <!-- Suggestions List -->
    <div v-else class="space-y-6">
      <div v-for="(categoryGroup, category) in groupedSuggestions" :key="category" class="space-y-4">
        <!-- Category Header -->
        <div class="flex items-center space-x-3 pb-2 border-b border-gray-200">
          <component :is="getCategoryIcon(category)" class="w-5 h-5 text-gray-500" />
          <h3 class="text-lg font-medium text-gray-900">{{ category }} Suggestions</h3>
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
            {{ categoryGroup.length }}
          </span>
        </div>

        <!-- Category Suggestions -->
        <div class="space-y-4">
          <div 
            v-for="suggestion in categoryGroup" 
            :key="suggestion.id" 
            class="border border-gray-200 rounded-lg"
          >
            <div class="p-6">
              <!-- Suggestion Header -->
              <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                  <div class="flex items-center mb-3">
                    <span 
                      class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border mr-3"
                      :class="getImpactColor(suggestion.impact_level)"
                    >
                      <component :is="getImpactIcon(suggestion.impact_level)" class="w-3 h-3 mr-1" />
                      {{ suggestion.impact_level }} Impact
                    </span>
                    <span 
                      class="text-xs font-medium"
                      :class="getEffortColor(suggestion.effort_level)"
                    >
                      {{ suggestion.effort_level }} Effort
                    </span>
                  </div>
                  <h4 class="text-lg font-medium text-gray-900 mb-2">{{ suggestion.title }}</h4>
                </div>
              </div>

              <!-- Main Description -->
              <div class="text-gray-700 leading-relaxed mb-4">
                <p>{{ suggestion.description }}</p>
              </div>

              <!-- Implementation Details -->
              <div v-if="suggestion.detailed_explanation" class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <h5 class="font-medium text-blue-900 mb-2 flex items-center">
                  <Info class="w-4 h-4 mr-2" />
                  Implementation Tips
                </h5>
                <p class="text-blue-800 text-sm leading-relaxed">{{ suggestion.detailed_explanation }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>

