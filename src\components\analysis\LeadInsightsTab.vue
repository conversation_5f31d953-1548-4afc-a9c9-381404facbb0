<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { supabase } from '../../lib/supabase';
import { generateAILeadInsights } from '../../lib/ai-contextual-analyzer';
import type { Database } from '../../types/supabase';
import { Users, MessageCircle, TrendingUp, Target, HelpCircle, Building, AlertCircle, Info, Lightbulb } from 'lucide-vue-next';
// @ts-ignore
import MarkdownIt from 'markdown-it';
import DOMPurify from 'dompurify';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

const aiLeadInsights = ref<{
  businessType: string;
  valueProposition: string;
  targetAudience: string;
  leadQuestions: Array<{
    questionType: string;
    questionText: string;
    contextExplanation: string;
    qualificationValue: string;
    suggestedResponse: string;
    priorityLevel: number;
  }>;
  businessImpact: string[];
  conversionBarriers: Array<{
    barrier: string;
    evidence: string;
    solution: string;
  }>;
} | null>(null);

const isLoadingAI = ref(false);
const aiError = ref<string | null>(null);
const showTooltip = ref<string | null>(null);

// Markdown renderer for target audience
const md = new MarkdownIt();
const renderMarkdown = (text: string) => {
  const renderedHtml = md.render(text);
  return DOMPurify.sanitize(renderedHtml);
};

onMounted(async () => {
  await loadAILeadInsights();
});

async function loadAILeadInsights() {
  if (!props.analysis) return;

  isLoadingAI.value = true;
  aiError.value = null;

  try {
    // Check if AI lead insights already exist in database with enhanced data
    const { data: existingInsights } = await supabase
      .from('lead_qualification_insights')
      .select('*')
      .eq('analysis_id', props.analysis.id)
      .order('priority_level', { ascending: false });

    if (existingInsights && existingInsights.length > 0) {
      // Use existing insights with enhanced data
      aiLeadInsights.value = {
        businessType: 'Business type identified from analysis',
        valueProposition: 'Value proposition analyzed',
        targetAudience: props.analysis.target_audience || 'Target audience identified',
        leadQuestions: existingInsights.map(insight => ({
          questionType: insight.question_type,
          questionText: insight.question_text,
          contextExplanation: insight.context_explanation || '',
          qualificationValue: insight.qualification_value || '',
          suggestedResponse: insight.suggested_response || '',
          priorityLevel: insight.priority_level || 3,
          businessImpactScore: insight.business_impact_score || 0,
          conversionRelevance: insight.conversion_relevance || '',
          followUpQuestions: insight.follow_up_questions || [],
          objectionHandling: insight.objection_handling || '',
          qualificationCriteria: insight.qualification_criteria || '',
          leadScoringWeight: insight.lead_scoring_weight || 1.0
        })),
        businessImpact: ['AI-generated business impact analysis available'],
        conversionBarriers: []
      };
    } else {
      // No insights found - content may still be generating
      aiError.value = 'Lead insights are being generated. Please wait for the analysis to complete.';
      aiLeadInsights.value = null;
    }
  } catch (error) {
    console.error('Error loading AI lead insights:', error);
    aiError.value = 'Failed to load lead insights. Please refresh the page or try again later.';
    aiLeadInsights.value = null;
  } finally {
    isLoadingAI.value = false;
  }
}

// Calculate lead generation score based on analysis data (0-10 scale)
const leadGenerationScore = computed(() => {
  const conversionScore = props.analysis.score || 0; // Already 0-10
  const performanceScore = props.analysis.performance_score || 0; // 0-100, convert to 0-10
  const seoScore = props.analysis.seo_score || 0; // 0-100, convert to 0-10

  // Weight the scores for lead generation potential (0-10 scale)
  const weightedScore = (conversionScore * 0.5) + ((performanceScore / 10) * 0.3) + ((seoScore / 10) * 0.2);
  return Math.round(weightedScore * 10) / 10; // Round to 1 decimal place
});

const getScoreColor = (score: number) => {
  if (score >= 8) return 'text-green-600';
  if (score >= 6) return 'text-blue-600';
  if (score >= 4) return 'text-yellow-600';
  if (score >= 2) return 'text-orange-600';
  return 'text-red-600';
};

const getScoreBg = (score: number) => {
  if (score >= 8) return 'bg-green-50 border-green-200';
  if (score >= 6) return 'bg-blue-50 border-blue-200';
  if (score >= 4) return 'bg-yellow-50 border-yellow-200';
  if (score >= 2) return 'bg-orange-50 border-orange-200';
  return 'bg-red-50 border-red-200';
};

const getScoreLabel = (score: number) => {
  if (score >= 8) return 'Excellent';
  if (score >= 6) return 'Good';
  if (score >= 4) return 'Fair';
  if (score >= 2) return 'Poor';
  return 'Critical';
};

const toggleTooltip = (tooltipId: string) => {
  showTooltip.value = showTooltip.value === tooltipId ? null : tooltipId;
};

const getTooltipContent = (metric: string) => {
  const tooltips = {
    leadScore: 'Lead Generation Score combines conversion optimization (50%), performance (30%), and SEO (20%) to predict lead capture potential. Scored 0-10.',
    conversionPriority: 'Shows which areas to focus on first for maximum lead generation impact. Based on current scores and improvement potential.',
    targetAudience: 'AI analysis of page content to identify the most likely prospects and their characteristics based on messaging and positioning.'
  };
  return tooltips[metric as keyof typeof tooltips] || '';
};

const getPhaseColor = (phase: string) => {
  if (phase.includes('Immediate')) return 'bg-red-100 text-red-800';
  if (phase.includes('Short-term')) return 'bg-yellow-100 text-yellow-800';
  return 'bg-blue-100 text-blue-800';
};

const getQuestionTypeColor = (type: string) => {
  const colors = {
    qualification: 'bg-blue-100 text-blue-800',
    concern: 'bg-orange-100 text-orange-800',
    business: 'bg-green-100 text-green-800',
    objection: 'bg-red-100 text-red-800'
  };
  return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
};

// Generate AI-based roadmap suggestions from analysis data
const roadmapSuggestions = computed(() => {
  if (!aiLeadInsights.value) return [];

  const conversionScore = props.analysis.score || 0;
  const performanceScore = props.analysis.performance_score || 0;
  const seoScore = props.analysis.seo_score || 0;

  const roadmap = [];

  // Immediate phase (0-2 weeks) - based on AI insights
  let immediateTitle = 'AI-Identified Quick Wins';
  let immediateDescription = '';
  let immediateImpact = '';

  if (conversionScore < 4) {
    immediateTitle = 'Critical Conversion Fixes';
    immediateDescription = `Based on AI analysis of ${aiLeadInsights.value.businessType.toLowerCase()} content, address fundamental conversion barriers preventing lead capture. Focus on ${aiLeadInsights.value.valueProposition} clarity and removing friction from key user flows.`;
    immediateImpact = '25-40% increase in lead capture rate';
  } else if (conversionScore < 6) {
    immediateTitle = 'Conversion Optimization';
    immediateDescription = `AI analysis reveals opportunities to enhance ${aiLeadInsights.value.businessType.toLowerCase()} conversion tactics. Implement proven strategies for ${aiLeadInsights.value.targetAudience.toLowerCase()} audience.`;
    immediateImpact = '15-25% increase in lead capture rate';
  } else {
    immediateTitle = 'Conversion Fine-tuning';
    immediateDescription = `AI analysis shows strong foundation. Optimize existing elements with A/B testing and minor improvements targeting ${aiLeadInsights.value.targetAudience.toLowerCase()}.`;
    immediateImpact = '10-15% increase in lead capture rate';
  }

  roadmap.push({
    phase: 'Immediate (0-2 weeks)',
    title: immediateTitle,
    description: immediateDescription,
    timeline: '1-2 weeks',
    expectedImpact: immediateImpact
  });

  // Short-term phase (1-3 months) - based on AI insights
  let shortTermTitle = 'AI-Guided Trust & Performance';
  let shortTermDescription = '';
  let shortTermImpact = '';

  if (performanceScore < 60) {
    shortTermTitle = 'Performance & Trust Building';
    shortTermDescription = `AI analysis indicates performance issues are hurting credibility for ${aiLeadInsights.value.businessType.toLowerCase()}. Improve page loading speeds and add trust signals specific to ${aiLeadInsights.value.targetAudience.toLowerCase()}.`;
    shortTermImpact = '20-35% improvement in lead quality and conversion';
  } else if (seoScore < 70) {
    shortTermTitle = 'SEO & Visibility Enhancement';
    shortTermDescription = `AI analysis reveals SEO opportunities for ${aiLeadInsights.value.businessType.toLowerCase()}. Optimize for search engines to increase organic traffic and add credibility elements.`;
    shortTermImpact = '15-25% improvement in lead volume and quality';
  } else {
    shortTermTitle = 'Advanced Trust Building';
    shortTermDescription = `AI analysis shows strong foundation. Implement sophisticated trust elements targeting ${aiLeadInsights.value.targetAudience.toLowerCase()} to qualify and convert high-value prospects.`;
    shortTermImpact = '15-25% improvement in lead quality';
  }

  roadmap.push({
    phase: 'Short-term (1-3 months)',
    title: shortTermTitle,
    description: shortTermDescription,
    timeline: '4-8 weeks',
    expectedImpact: shortTermImpact
  });

  // Long-term phase (3-6 months) - based on AI insights
  const overallScore = Math.round((conversionScore + (performanceScore / 10) + (seoScore / 10)) / 3);
  let longTermTitle = 'AI-Optimized Lead System';
  let longTermDescription = '';
  let longTermImpact = '';

  if (overallScore < 5) {
    longTermTitle = 'Complete Lead System Overhaul';
    longTermDescription = `AI analysis recommends comprehensive lead generation system for ${aiLeadInsights.value.businessType.toLowerCase()} with progressive profiling, automated nurturing, and advanced analytics.`;
    longTermImpact = '40-60% increase in qualified leads and revenue';
  } else if (overallScore < 7) {
    longTermTitle = 'Lead Intelligence & Automation';
    longTermDescription = `AI insights suggest deploying lead scoring, behavioral tracking, and automated qualification processes optimized for ${aiLeadInsights.value.targetAudience.toLowerCase()}.`;
    longTermImpact = '25-40% increase in qualified leads';
  } else {
    longTermTitle = 'Premium Lead Experience';
    longTermDescription = `AI analysis indicates readiness for personalized lead journeys with dynamic content, AI-powered recommendations, and premium touchpoints for ${aiLeadInsights.value.targetAudience.toLowerCase()}.`;
    longTermImpact = '20-30% increase in high-value qualified leads';
  }

  roadmap.push({
    phase: 'Long-term (3-6 months)',
    title: longTermTitle,
    description: longTermDescription,
    timeline: '3-6 months',
    expectedImpact: longTermImpact
  });

  return roadmap;
});

// Business impact insights based on AI analysis
const businessImpact = computed(() => {
  if (!aiLeadInsights.value) return [];
  
  return aiLeadInsights.value.businessImpact.length > 0 
    ? aiLeadInsights.value.businessImpact 
    : [
        `AI analysis reveals optimization potential for ${aiLeadInsights.value.businessType.toLowerCase()}`,
        `Target audience insights show opportunities with ${aiLeadInsights.value.targetAudience.toLowerCase()}`,
        'Implementing AI recommendations could increase lead quality by 25-40%',
        'Performance improvements directly correlate with lead conversion rates'
      ];
});
</script>

<template>
  <div class="space-y-8">
    <!-- AI Lead Generation Overview -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <Users class="w-5 h-5 text-gray-400 mr-2" />
          <h3 class="text-lg font-semibold text-gray-900">AI-Powered Lead Generation Analysis</h3>
          

        </div>
        
        <!-- Lead Generation Score -->
        <div class="flex items-center space-x-3">
          <div class="text-right">
            <p class="text-sm text-gray-500">Lead Gen Score</p>
          </div>
          <div 
            class="w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold"
            :class="getScoreBg(leadGenerationScore) + ' ' + getScoreColor(leadGenerationScore)"
          >
            {{ Math.round(leadGenerationScore) }}
          </div>
        </div>
      </div>

      <!-- AI Lead Insights Content -->
      <div v-if="aiLeadInsights" class="space-y-6">
        <!-- Business Context -->
        <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
          <h4 class="font-medium text-purple-900 mb-3 flex items-center">
            <Target class="w-4 h-4 mr-2" />
            AI Business Analysis
          </h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <p class="font-medium text-purple-900">Business Type</p>
              <p class="text-purple-800">{{ aiLeadInsights.businessType }}</p>
            </div>
            <div>
              <p class="font-medium text-purple-900">Target Audience</p>
              <div
                class="text-purple-800 prose prose-sm max-w-none"
                v-html="renderMarkdown(aiLeadInsights.targetAudience)"
              ></div>
            </div>
            <div>
              <p class="font-medium text-purple-900">Value Proposition</p>
              <p class="text-purple-800">{{ aiLeadInsights.valueProposition }}</p>
            </div>
          </div>
        </div>

        <!-- AI-Generated Lead Questions -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <div class="flex items-center mb-4">
            <MessageCircle class="w-5 h-5 text-gray-400 mr-2" />
            <h4 class="text-lg font-semibold text-gray-900">AI-Generated Lead Qualification Questions</h4>
          </div>
          <p class="text-gray-600 mb-6">Based on AI analysis of your page content, potential leads might ask these specific questions:</p>
          
          <div class="space-y-4">
            <div 
              v-for="(question, index) in aiLeadInsights.leadQuestions" 
              :key="index"
              class="border border-gray-200 rounded-lg p-4"
            >
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                  <div class="flex items-center mb-2">
                    <span 
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mr-2"
                      :class="getQuestionTypeColor(question.questionType)"
                    >
                      {{ question.questionType }}
                    </span>
                    <span class="text-xs text-gray-500">Priority: {{ question.priorityLevel }}/5</span>
                  </div>
                  <h5 class="font-medium text-gray-900 mb-2">{{ question.questionText }}</h5>
                </div>
              </div>

              <div class="space-y-2 text-sm">
                <div class="bg-blue-50 rounded p-3 border border-blue-200">
                  <p class="font-medium text-blue-900 mb-1">Why this question arises:</p>
                  <p class="text-blue-800">{{ question.contextExplanation }}</p>
                </div>
                
                <div class="bg-green-50 rounded p-3 border border-green-200">
                  <p class="font-medium text-green-900 mb-1">Qualification value:</p>
                  <p class="text-green-800">{{ question.qualificationValue }}</p>
                </div>
                
                <div class="bg-gray-50 rounded p-3 border border-gray-200">
                  <p class="font-medium text-gray-900 mb-1">Suggested response:</p>
                  <p class="text-gray-800">{{ question.suggestedResponse }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI-Generated Conversion Barriers -->
        <div v-if="aiLeadInsights.conversionBarriers.length > 0" class="bg-white rounded-lg border border-gray-200 p-6">
          <div class="flex items-center mb-4">
            <AlertTriangle class="w-5 h-5 text-gray-400 mr-2" />
            <h4 class="text-lg font-semibold text-gray-900">AI-Identified Conversion Barriers</h4>
          </div>
          
          <div class="space-y-4">
            <div 
              v-for="(barrier, index) in aiLeadInsights.conversionBarriers" 
              :key="index"
              class="border border-red-200 rounded-lg p-4 bg-red-50"
            >
              <h5 class="font-medium text-red-900 mb-2">{{ barrier.barrier }}</h5>
              <div class="space-y-2 text-sm">
                <div>
                  <p class="font-medium text-red-800">Evidence found:</p>
                  <p class="text-red-700">{{ barrier.evidence }}</p>
                </div>
                <div class="bg-red-100 rounded p-2 border border-red-300">
                  <p class="font-medium text-red-900 mb-1">
                    <Lightbulb class="w-3 h-3 inline mr-1" />
                    AI Recommendation:
                  </p>
                  <p class="text-red-800">{{ barrier.solution }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI-Generated Implementation Roadmap -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <div class="flex items-center mb-6">
            <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            <h3 class="text-lg font-semibold text-gray-900">AI-Generated Lead Generation Roadmap</h3>
          </div>
          
          <div class="space-y-6">
            <div 
              v-for="(suggestion, index) in roadmapSuggestions" 
              :key="index"
              class="border border-gray-200 rounded-lg p-6"
            >
              <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                  <span 
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                    :class="getPhaseColor(suggestion.phase)"
                  >
                    {{ suggestion.phase }}
                  </span>
                </div>
                <div class="text-right text-sm text-gray-500">
                  {{ suggestion.timeline }}
                </div>
              </div>
              
              <h4 class="text-lg font-medium text-gray-900 mb-2">{{ suggestion.title }}</h4>
              <p class="text-gray-700 mb-4">{{ suggestion.description }}</p>
              
              <div class="bg-green-50 rounded-lg p-3 border border-green-200">
                <div class="flex items-center">
                  <TrendingUp class="w-4 h-4 text-green-600 mr-2" />
                  <span class="text-sm font-medium text-green-900">Expected Impact:</span>
                  <span class="text-sm text-green-800 ml-2">{{ suggestion.expectedImpact }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Business Impact Analysis -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <div class="flex items-center mb-6">
            <TrendingUp class="w-5 h-5 text-gray-400 mr-2" />
            <h3 class="text-lg font-semibold text-gray-900">AI Business Impact Analysis</h3>
          </div>
          
          <div class="space-y-3">
            <div 
              v-for="(insight, index) in businessImpact" 
              :key="index"
              class="flex items-start p-3 bg-blue-50 rounded-lg border border-blue-200"
            >
              <svg class="w-4 h-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="text-sm text-blue-800">{{ insight }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="aiError" class="text-center py-12">
        <AlertTriangle class="w-8 h-8 text-yellow-500 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">AI Analysis Unavailable</h3>
        <p class="text-gray-600 mb-4">{{ aiError }}</p>
        <button 
          @click="loadAILeadInsights" 
          class="btn-primary"
        >
          Retry AI Analysis
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}
</style>