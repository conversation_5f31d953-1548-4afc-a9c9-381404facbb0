<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { supabase } from '../../lib/supabase';
import { Plus, <PERSON><PERSON>heck, TriangleAlert, FileText } from 'lucide-vue-next';
import type { Database } from '../../types/supabase';
import DashboardSkeleton from './DashboardSkeleton.vue';
import { useMotion } from '@vueuse/motion';

type Analysis = Database['public']['Tables']['analyses']['Row'];

interface DashboardStats {
  totalAnalyses: number;
  averageScore: number;
  averageConversionRate: number;
  totalSuggestions: number;
  priorityIssues: number;
}

const analyses = ref<Analysis[]>([]);
const stats = ref<DashboardStats>({
  totalAnalyses: 0,
  averageScore: 0,
  averageConversionRate: 0,
  totalSuggestions: 0,
  priorityIssues: 0
});
const loading = ref(true);
const error = ref<string | null>(null);

const loadDashboardData = async () => {
  try {
    loading.value = true;
    error.value = null;

    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      throw new Error('User not authenticated');
    }

    // Fetch recent analyses from the real database
    const { data: analysesData, error: analysesError } = await supabase
      .from('analyses')
      .select('*')
      .eq('user_id', userData.user.id)
      .order('created_at', { ascending: false })
      .limit(5);

    if (analysesError) {
      throw analysesError;
    }

    analyses.value = analysesData || [];

    // Calculate stats from real data
    if (analyses.value.length > 0) {
      stats.value = {
        totalAnalyses: analyses.value.length,
        averageScore: analyses.value.length > 0
          ? Math.round((analyses.value.reduce((sum, a) => sum + a.score, 0) / analyses.value.length) * 10) / 10
          : 0,
        averageConversionRate: analyses.value.length > 0
          ? Math.round((analyses.value.reduce((sum, a) => sum + a.conversion_rate, 0) / analyses.value.length) * 10) / 10
          : 0,
        totalSuggestions: analyses.value.reduce((sum, a) => sum + (a.suggestions_count || 0), 0),
        priorityIssues: analyses.value.reduce((sum, a) => sum + (a.priority_issues_count || 0), 0)
      };
    } else {
      stats.value = {
        totalAnalyses: 0,
        averageScore: 0,
        averageConversionRate: 0,
        totalSuggestions: 0,
        priorityIssues: 0
      };
    }

  } catch (err) {
    console.error('Error loading dashboard data:', err);
    error.value = err instanceof Error ? err.message : 'Failed to load dashboard data';

    // Show empty state on error
    analyses.value = [];
    stats.value = {
      totalAnalyses: 0,
      averageScore: 0,
      averageConversionRate: 0,
      totalSuggestions: 0,
      priorityIssues: 0
    };
  } finally {
    loading.value = false;
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', { 
    month: 'short', 
    day: 'numeric',
    year: 'numeric'
  }).format(date);
};



const hasAnalyses = computed(() => analyses.value.length > 0);

onMounted(() => {
  loadDashboardData();
});
</script>

<template>
  <div class="max-w-7xl mx-auto px-6 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900 mb-1">
            Dashboard
          </h1>
          <p class="text-gray-600">
            Manage and review your landing page analyses
          </p>
        </div>
        <a
          href="/dashboard/analysis/new"
          class="btn-primary"
        >
        <Plus class="w-4 h-4 mr-2"/>
          New Analysis
        </a>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading">
      <DashboardSkeleton />
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 bg-error-50 rounded-full flex items-center justify-center">
        <svg class="w-8 h-8 text-error-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Dashboard</h3>
      <p class="text-gray-600 mb-6">{{ error }}</p>
      <button @click="loadDashboardData" class="btn-primary">
        Try Again
      </button>
    </div>

    <!-- Dashboard Content -->
    <div
      v-else
      class="animate-fade-in"
    >
      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div
          class="metric-card"
        >
          <div class="flex items-center">
            <div class="p-3 bg-primary-50 rounded-xl">
              <svg class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="metric-label">Total Analyses</p>
              <p class="metric-value">{{ stats.totalAnalyses }}</p>
            </div>
          </div>
        </div>
        
        <div
          class="metric-card"
        >
          <div class="flex items-center">
            <div class="p-3 bg-success-50 rounded-xl">
              <ListCheck class="h-6 w-6 text-success-600"/>
            </div>
            <div class="ml-4">
              <p class="metric-label">Avg. Score</p>
              <p class="metric-value">{{ stats.averageScore }}/10</p>
            </div>
          </div>
        </div>

        <div
          class="metric-card"
        >
          <div class="flex items-center">
            <div class="p-3 bg-blue-50 rounded-xl">
              <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="metric-label">Total Suggestions</p>
              <p class="metric-value">{{ stats.totalSuggestions }}</p>
            </div>
          </div>
        </div>

        <div
          class="metric-card"
        >
          <div class="flex items-center">
            <div class="p-3 bg-red-50 rounded-xl">
              <TriangleAlert class="h-6 w-6 text-red-600"/>
            </div>
            <div class="ml-4">
              <p class="metric-label">Priority Issues</p>
              <p class="metric-value">{{ stats.priorityIssues }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Analyses -->
      <div
        class="card"
      >
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900">Recent Analyses</h2>
            <a 
              href="/dashboard/history" 
              class="text-primary-600 hover:text-primary-700 font-medium text-sm transition-colors"
            >
              View all →
            </a>
          </div>
        </div>
        
        <div class="p-6">
          <!-- No Analyses State -->
          <div v-if="!hasAnalyses" class="text-center py-12">
            <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <FileText class="w-8 h-8 text-gray-400"/>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No analyses yet</h3>
            <p class="text-gray-500 mb-6">Get started by analyzing your first landing page</p>
            <a 
              href="/dashboard/analysis/new"
              class="btn-primary"
            >
              <Plus class="w-4 h-4 mr-2"/>
              Create Analysis
            </a>
          </div>

          <!-- Analyses List -->
          <div v-else class="space-y-4">
            <a
              v-for="(analysis, index) in analyses"
              :key="analysis.id"
              :href="`/dashboard/analysis/${analysis.id}`"
              class="card-interactive p-4"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="w-10 h-10 bg-primary-50 rounded-lg flex items-center justify-center">
                    <FileText class="w-5 h-5 text-primary-600"/>
                  </div>
                  <div>
                    <h3 class="font-medium text-gray-900">{{ analysis.title || analysis.url }}</h3>
                    <p class="text-sm text-gray-500">{{ analysis.url }}</p>
                    <p class="text-xs text-gray-400">Analyzed on {{ formatDate(analysis.created_at) }}</p>
                  </div>
                </div>
                <div class="flex items-center space-x-4">
                  <div class="text-right">
                    <p class="text-sm font-medium text-gray-900">Score</p>
                    <p class="text-lg font-bold text-gray-900">{{ analysis.score }}/10</p>
                  </div>
                  <div class="text-right">
                    <p class="text-sm font-medium text-gray-900">Conversion Rate</p>
                    <p class="text-lg font-bold text-gray-900">{{ analysis.conversion_rate }}%</p>
                  </div>
                  <span class="badge-success">✓ Completed</span>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
