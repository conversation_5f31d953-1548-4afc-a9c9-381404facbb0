/**
 * Enhanced AI prompt templates for generating contextual, website-specific insights
 * These templates incorporate actual website data (HTML, Lighthouse, scraped content)
 * to replace generic score-based logic with actionable, specific recommendations.
 */

export interface WebsiteContext {
  url: string;
  htmlContent: string;
  title: string;
  metaDescription?: string;
  headings: string[];
  images: string[];
  links: string[];
  performanceData?: any;
  lighthouseData?: any;
  seoData?: any;
  scrapedContent?: any;
}

export interface AIInsightRequest {
  context: WebsiteContext;
  analysisType: 'pros-cons' | 'performance-impact' | 'lead-insights' | 'page-summary' | 'performance-seo-summary';
  specificFocus?: string;
}

/**
 * Generate contextual pros and cons based on actual website analysis
 */
export function createProsConsPrompt(context: WebsiteContext): string {
  return `You are an expert conversion rate optimization consultant. Analyze this website and provide specific, actionable pros and cons based on the actual content and structure.

WEBSITE CONTEXT:
- URL: ${context.url}
- Title: ${context.title}
- Meta Description: ${context.metaDescription || 'Not provided'}
- Main Headings: ${context.headings.slice(0, 5).join(', ')}
- Performance Score: ${context.performanceData?.performanceScore || 'Not available'}
- SEO Score: ${context.seoData?.score || 'Not available'}

HTML CONTENT ANALYSIS:
${context.htmlContent.substring(0, 3000)}...

PERFORMANCE DATA:
${context.performanceData ? JSON.stringify(context.performanceData, null, 2).substring(0, 1000) : 'Performance analysis pending'}

INSTRUCTIONS:
1. Analyze the ACTUAL website content, not just scores
2. Identify specific strengths based on detected elements (forms, CTAs, content quality, etc.)
3. Identify specific weaknesses based on missing elements or poor implementation
4. Each insight should reference actual page elements or detected issues
5. Provide actionable recommendations, not generic advice
6. Focus on conversion optimization impact

Return a JSON object with this structure:
{
  "strengths": [
    {
      "insight": "Specific strength with reference to actual page element",
      "evidence": "What you observed in the content/code",
      "impact": "How this helps conversions"
    }
  ],
  "weaknesses": [
    {
      "insight": "Specific weakness with reference to actual page element", 
      "evidence": "What you observed missing or poorly implemented",
      "impact": "How this hurts conversions",
      "recommendation": "Specific action to fix this issue"
    }
  ],
  "overallAssessment": "Brief summary of the page's conversion potential based on actual analysis"
}

Limit to 6 strengths and 6 weaknesses maximum. Be specific and actionable.`;
}

/**
 * Generate performance impact analysis based on actual Lighthouse data
 */
export function createPerformanceImpactPrompt(context: WebsiteContext): string {
  return `You are a web performance expert specializing in Core Web Vitals and conversion optimization. Analyze the actual performance data and provide specific, actionable insights.

WEBSITE CONTEXT:
- URL: ${context.url}
- Title: ${context.title}

ACTUAL LIGHTHOUSE DATA:
${context.lighthouseData ? JSON.stringify(context.lighthouseData, null, 2) : 'Lighthouse data not available'}

PERFORMANCE METRICS:
${context.performanceData ? JSON.stringify(context.performanceData, null, 2) : 'Performance metrics not available'}

HTML CONTENT (for context):
${context.htmlContent.substring(0, 2000)}...

INSTRUCTIONS:
1. Analyze the ACTUAL Lighthouse audit results, not generic scores
2. Identify specific performance bottlenecks from the audit data
3. Calculate realistic conversion impact based on actual metrics
4. Provide specific, implementable recommendations
5. Reference actual detected issues (large images, render-blocking resources, etc.)

Return a JSON object with this structure:
{
  "primaryIssue": {
    "title": "Concise header (≤10 words) describing the main performance problem",
    "description": "Detailed explanation of the specific issue found in the audit",
    "evidence": "Specific metrics or audit findings that support this",
    "conversionImpact": "Realistic estimate of conversion impact with reasoning"
  },
  "keyFindings": [
    {
      "metric": "Specific Core Web Vital or performance metric",
      "currentValue": "Actual measured value",
      "targetValue": "Recommended target",
      "impact": "How this affects user experience and conversions"
    }
  ],
  "recommendations": [
    {
      "title": "Specific, actionable recommendation header",
      "description": "Detailed implementation steps",
      "expectedImprovement": "Realistic performance gain",
      "priority": "High/Medium/Low based on impact vs effort"
    }
  ],
  "businessImpact": "Overall assessment of how performance affects this specific website's business goals"
}

Focus on actionable insights based on actual audit data, not generic performance advice.`;
}

/**
 * Generate lead insights based on actual page content and user journey analysis
 */
export function createLeadInsightsPrompt(context: WebsiteContext): string {
  return `You are a lead generation and sales psychology expert. Analyze this actual website content to identify realistic questions, concerns, and objections that prospects would have.

WEBSITE CONTEXT:
- URL: ${context.url}
- Title: ${context.title}
- Meta Description: ${context.metaDescription || 'Not provided'}

ACTUAL PAGE CONTENT:
${context.htmlContent.substring(0, 4000)}...

HEADINGS STRUCTURE:
${context.headings.join('\n')}

DETECTED ELEMENTS:
- Images: ${context.images.length} found
- Links: ${context.links.length} found
- Forms: ${context.htmlContent.includes('<form') ? 'Contact/signup forms detected' : 'No forms detected'}
- Pricing: ${context.htmlContent.toLowerCase().includes('price') || context.htmlContent.toLowerCase().includes('$') ? 'Pricing information present' : 'No pricing information visible'}

INSTRUCTIONS:
1. Analyze the ACTUAL content, value proposition, and messaging
2. Identify the specific business type and target audience from content analysis
3. Generate realistic questions prospects would have after reading THIS specific page
4. Identify concerns based on what's missing or unclear in the content
5. Consider the user journey and what information gaps exist
6. Focus on lead qualification and sales enablement

Return a JSON object with this structure:
{
  "businessType": "Specific business type identified from content analysis",
  "valueProposition": "Main value proposition extracted from the page",
  "targetAudience": "Specific target audience identified from content and messaging",
  "leadQuestions": [
    {
      "question": "Specific question a prospect would ask after visiting this page",
      "context": "Why this question arises from the content",
      "qualification": "How this question helps qualify the lead"
    }
  ],
  "leadConcerns": [
    {
      "concern": "Specific concern or objection based on page content",
      "trigger": "What in the content (or missing from it) causes this concern",
      "resolution": "How to address this concern in sales conversations"
    }
  ],
  "conversionBarriers": [
    {
      "barrier": "Specific element preventing conversion",
      "evidence": "What you observed in the content",
      "solution": "Specific recommendation to remove this barrier"
    }
  ],
  "leadScoringFactors": [
    {
      "factor": "Behavioral indicator that suggests lead quality",
      "reasoning": "Why this indicates purchase intent or fit"
    }
  ]
}

Generate 4-6 items for each array. Be specific to this website's actual content and business model.`;
}

/**
 * Generate page summary based on actual content analysis
 */
export function createPageSummaryPrompt(context: WebsiteContext): string {
  return `You are a UX and content strategy expert. Analyze this actual website page to create a comprehensive summary of what visitors experience and take away.

WEBSITE CONTEXT:
- URL: ${context.url}
- Title: ${context.title}
- Meta Description: ${context.metaDescription || 'Not provided'}

ACTUAL PAGE CONTENT:
${context.htmlContent.substring(0, 4000)}...

CONTENT STRUCTURE:
- Main Headings: ${context.headings.slice(0, 8).join(' | ')}
- Key Sections: ${context.htmlContent.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi)?.slice(0, 5).join(', ') || 'No clear sections detected'}

INSTRUCTIONS:
1. Analyze the ACTUAL page content, not assumptions
2. Identify the main value proposition from the content
3. Assess hero section effectiveness based on actual messaging
4. Evaluate content clarity and user journey
5. Identify what visitors would understand and remember

Return a JSON object with this structure:
{
  "pageOverview": {
    "businessType": "Specific business type identified from content",
    "mainPurpose": "Primary goal/purpose of this page",
    "targetAudience": "Who this page is designed for based on content analysis"
  },
  "valueProposition": {
    "primary": "Main value proposition extracted from hero/header content",
    "supporting": ["Secondary value propositions or benefits mentioned"],
    "clarity": "Assessment of how clear the value proposition is"
  },
  "heroSectionAnalysis": {
    "headline": "Actual headline text from the page",
    "effectiveness": "Assessment of headline hook and clarity",
    "cta": "Primary call-to-action text and placement",
    "improvements": "Specific suggestions for hero section optimization"
  },
  "contentQuality": {
    "keyTakeaways": ["What visitors would remember after reading"],
    "informationGaps": ["Important information that's missing"],
    "userJourney": "How well the content guides users toward conversion"
  },
  "conversionElements": {
    "strengths": ["Conversion elements that work well"],
    "weaknesses": ["Missing or poorly implemented conversion elements"],
    "recommendations": ["Specific improvements for better conversion"]
  }
}

Base all analysis on actual page content, not generic assumptions.`;
}

/**
 * Generate performance & SEO summary based on actual audit data
 */
export function createPerformanceSEOSummaryPrompt(context: WebsiteContext): string {
  return `You are a technical SEO and web performance expert. Analyze the actual audit data to provide specific, actionable insights.

WEBSITE CONTEXT:
- URL: ${context.url}
- Title: ${context.title}

ACTUAL PERFORMANCE DATA:
${context.performanceData ? JSON.stringify(context.performanceData, null, 2) : 'Performance data not available'}

ACTUAL SEO DATA:
${context.seoData ? JSON.stringify(context.seoData, null, 2) : 'SEO data not available'}

LIGHTHOUSE AUDIT RESULTS:
${context.lighthouseData ? JSON.stringify(context.lighthouseData, null, 2).substring(0, 2000) : 'Lighthouse data not available'}

HTML ANALYSIS:
${context.htmlContent.substring(0, 2000)}...

INSTRUCTIONS:
1. Analyze ACTUAL audit results and detected issues
2. Identify specific technical problems from the data
3. Provide actionable recommendations with implementation details
4. Calculate realistic business impact based on actual metrics
5. Reference specific audit findings, not generic advice

Return a JSON object with this structure:
{
  "executiveSummary": "High-level assessment based on actual audit results",
  "performanceFindings": [
    {
      "issue": "Specific performance issue detected in audit",
      "impact": "How this affects user experience and business metrics",
      "solution": "Detailed implementation steps to fix",
      "priority": "High/Medium/Low based on impact and effort"
    }
  ],
  "seoFindings": [
    {
      "issue": "Specific SEO issue detected in audit",
      "impact": "How this affects search visibility and traffic",
      "solution": "Detailed implementation steps to fix",
      "priority": "High/Medium/Low based on impact and effort"
    }
  ],
  "businessImpact": {
    "currentState": "Assessment of current performance/SEO impact on business",
    "potentialGains": "Realistic improvements possible with recommended changes",
    "timeline": "Expected timeframe for improvements"
  },
  "actionPlan": [
    {
      "phase": "Phase 1/2/3",
      "tasks": ["Specific tasks to complete in this phase"],
      "expectedOutcome": "What improvements to expect",
      "timeframe": "Realistic completion timeline"
    }
  ]
}

Focus on specific, implementable recommendations based on actual audit findings.`;
}
