/*
  # AI-Driven Insights Schema Enhancement

  1. New Tables
    - `ai_insights` - Stores AI-generated contextual insights
    - `website_context` - Stores extracted website data for AI analysis
    - `insight_categories` - Categorizes different types of insights
  
  2. Enhanced Tables
    - `analyses` - Add AI insight tracking and context storage
    - `suggestions` - Enhanced with AI-generated implementation details
  
  3. Security
    - Enable RLS on all new tables
    - Add policies for authenticated users
  
  4. Functions
    - AI insight generation triggers
    - Context extraction utilities
*/

-- Create insight categories table
CREATE TABLE IF NOT EXISTS insight_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text UNIQUE NOT NULL,
  description text,
  icon text,
  color text,
  created_at timestamptz DEFAULT now()
);

-- Insert default categories
INSERT INTO insight_categories (name, description, icon, color) VALUES
('conversion', 'Conversion rate optimization insights', 'trending-up', 'blue'),
('performance', 'Website performance and speed insights', 'zap', 'green'),
('seo', 'Search engine optimization insights', 'search', 'purple'),
('content', 'Content quality and messaging insights', 'file-text', 'orange'),
('trust', 'Trust signals and credibility insights', 'shield', 'indigo'),
('ux', 'User experience and design insights', 'users', 'pink')
ON CONFLICT (name) DO NOTHING;

-- Create AI insights table for storing contextual insights
CREATE TABLE IF NOT EXISTS ai_insights (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
  category_id uuid NOT NULL REFERENCES insight_categories(id),
  insight_type text NOT NULL CHECK (insight_type IN ('strength', 'weakness', 'opportunity', 'recommendation')),
  title text NOT NULL, -- Concise header (≤10 words)
  description text NOT NULL, -- Main insight description
  evidence text, -- What was observed in the website
  impact_explanation text, -- How this affects conversions/UX
  implementation_steps text, -- Specific steps to fix/improve
  business_value text, -- Business impact explanation
  priority_score integer DEFAULT 0 CHECK (priority_score >= 0 AND priority_score <= 100),
  confidence_score integer DEFAULT 0 CHECK (confidence_score >= 0 AND confidence_score <= 100),
  ai_model text DEFAULT 'qwen-2.5-72b',
  generated_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- Create website context table for storing extracted data
CREATE TABLE IF NOT EXISTS website_context (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
  url text NOT NULL,
  html_content text,
  page_title text,
  meta_description text,
  headings jsonb DEFAULT '[]'::jsonb,
  images jsonb DEFAULT '[]'::jsonb,
  links jsonb DEFAULT '[]'::jsonb,
  business_type text,
  target_audience_detected text,
  value_proposition text,
  conversion_elements jsonb DEFAULT '{}'::jsonb,
  content_analysis jsonb DEFAULT '{}'::jsonb,
  extracted_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- Create lead qualification insights table
CREATE TABLE IF NOT EXISTS lead_qualification_insights (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
  question_type text NOT NULL CHECK (question_type IN ('qualification', 'concern', 'business', 'objection')),
  question_text text NOT NULL,
  context_explanation text, -- Why this question arises
  qualification_value text, -- How this helps qualify leads
  suggested_response text, -- How to address this question
  priority_level integer DEFAULT 0 CHECK (priority_level >= 1 AND priority_level <= 5),
  ai_generated boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Create performance impact insights table
CREATE TABLE IF NOT EXISTS performance_impact_insights (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  analysis_id uuid NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
  impact_type text NOT NULL CHECK (impact_type IN ('critical', 'opportunity', 'optimization')),
  metric_name text NOT NULL,
  current_value numeric,
  target_value numeric,
  impact_description text NOT NULL,
  conversion_impact text, -- How this affects conversions
  implementation_guide text, -- Step-by-step fix instructions
  expected_improvement text, -- Realistic improvement estimate
  effort_level text CHECK (effort_level IN ('Low', 'Medium', 'High')),
  timeline text, -- Expected implementation time
  created_at timestamptz DEFAULT now()
);

-- Enhance analyses table with AI context tracking
ALTER TABLE analyses 
ADD COLUMN IF NOT EXISTS ai_insights_generated boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS context_extracted boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS ai_analysis_version text DEFAULT '1.0',
ADD COLUMN IF NOT EXISTS website_business_type text,
ADD COLUMN IF NOT EXISTS ai_target_audience text,
ADD COLUMN IF NOT EXISTS ai_value_proposition text,
ADD COLUMN IF NOT EXISTS lead_generation_score numeric CHECK (lead_generation_score >= 0 AND lead_generation_score <= 10),
ADD COLUMN IF NOT EXISTS conversion_barriers jsonb DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS trust_signals jsonb DEFAULT '[]'::jsonb;

-- Enhance suggestions table with AI-generated details
ALTER TABLE suggestions 
ADD COLUMN IF NOT EXISTS ai_generated boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS evidence_found text,
ADD COLUMN IF NOT EXISTS conversion_impact text,
ADD COLUMN IF NOT EXISTS implementation_complexity text,
ADD COLUMN IF NOT EXISTS success_metrics text,
ADD COLUMN IF NOT EXISTS related_insights uuid[] DEFAULT '{}';

-- Enable RLS on new tables
ALTER TABLE ai_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE website_context ENABLE ROW LEVEL SECURITY;
ALTER TABLE lead_qualification_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_impact_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE insight_categories ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can read own AI insights"
  ON ai_insights FOR SELECT
  TO authenticated
  USING (
    analysis_id IN (
      SELECT id FROM analyses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can read own website context"
  ON website_context FOR SELECT
  TO authenticated
  USING (
    analysis_id IN (
      SELECT id FROM analyses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can read own lead insights"
  ON lead_qualification_insights FOR SELECT
  TO authenticated
  USING (
    analysis_id IN (
      SELECT id FROM analyses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can read own performance insights"
  ON performance_impact_insights FOR SELECT
  TO authenticated
  USING (
    analysis_id IN (
      SELECT id FROM analyses WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Everyone can read insight categories"
  ON insight_categories FOR SELECT
  TO authenticated
  USING (true);

-- Service role policies for AI generation
CREATE POLICY "Service role can manage AI insights"
  ON ai_insights FOR ALL
  TO service_role
  USING (true);

CREATE POLICY "Service role can manage website context"
  ON website_context FOR ALL
  TO service_role
  USING (true);

CREATE POLICY "Service role can manage lead insights"
  ON lead_qualification_insights FOR ALL
  TO service_role
  USING (true);

CREATE POLICY "Service role can manage performance insights"
  ON performance_impact_insights FOR ALL
  TO service_role
  USING (true);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_insights_analysis_id ON ai_insights(analysis_id);
CREATE INDEX IF NOT EXISTS idx_ai_insights_category ON ai_insights(category_id);
CREATE INDEX IF NOT EXISTS idx_ai_insights_type ON ai_insights(insight_type);
CREATE INDEX IF NOT EXISTS idx_website_context_analysis_id ON website_context(analysis_id);
CREATE INDEX IF NOT EXISTS idx_lead_insights_analysis_id ON lead_qualification_insights(analysis_id);
CREATE INDEX IF NOT EXISTS idx_lead_insights_type ON lead_qualification_insights(question_type);
CREATE INDEX IF NOT EXISTS idx_performance_insights_analysis_id ON performance_impact_insights(analysis_id);
CREATE INDEX IF NOT EXISTS idx_performance_insights_type ON performance_impact_insights(impact_type);

-- Create function to extract website context
CREATE OR REPLACE FUNCTION extract_website_context(
  p_analysis_id uuid,
  p_html_content text,
  p_scraped_data jsonb
)
RETURNS void AS $$
DECLARE
  v_business_type text;
  v_target_audience text;
  v_value_proposition text;
  v_headings jsonb;
  v_images jsonb;
  v_links jsonb;
BEGIN
  -- Extract headings from scraped data
  v_headings := COALESCE(p_scraped_data->'headings', '[]'::jsonb);
  v_images := COALESCE(p_scraped_data->'images', '[]'::jsonb);
  v_links := COALESCE(p_scraped_data->'links', '[]'::jsonb);
  
  -- Detect business type from content
  v_business_type := CASE
    WHEN p_html_content ILIKE '%shop%' OR p_html_content ILIKE '%buy%' OR p_html_content ILIKE '%cart%' THEN 'E-commerce'
    WHEN p_html_content ILIKE '%saas%' OR p_html_content ILIKE '%software%' OR p_html_content ILIKE '%app%' THEN 'SaaS'
    WHEN p_html_content ILIKE '%service%' OR p_html_content ILIKE '%consulting%' THEN 'Service Business'
    WHEN p_html_content ILIKE '%blog%' OR p_html_content ILIKE '%article%' THEN 'Content/Media'
    ELSE 'General Business'
  END;
  
  -- Detect target audience
  v_target_audience := CASE
    WHEN p_html_content ILIKE '%enterprise%' OR p_html_content ILIKE '%business%' THEN 'Business/Enterprise'
    WHEN p_html_content ILIKE '%developer%' OR p_html_content ILIKE '%technical%' THEN 'Developers/Technical'
    WHEN p_html_content ILIKE '%small business%' OR p_html_content ILIKE '%startup%' THEN 'Small Business/Startups'
    ELSE 'General Audience'
  END;
  
  -- Extract value proposition (first heading or title)
  v_value_proposition := COALESCE(
    v_headings->0->>'text',
    (SELECT title FROM analyses WHERE id = p_analysis_id),
    'Value proposition analysis needed'
  );
  
  -- Insert or update website context
  INSERT INTO website_context (
    analysis_id,
    url,
    html_content,
    page_title,
    meta_description,
    headings,
    images,
    links,
    business_type,
    target_audience_detected,
    value_proposition,
    conversion_elements,
    content_analysis
  )
  VALUES (
    p_analysis_id,
    (SELECT url FROM analyses WHERE id = p_analysis_id),
    p_html_content,
    (SELECT title FROM analyses WHERE id = p_analysis_id),
    p_scraped_data->>'metaDescription',
    v_headings,
    v_images,
    v_links,
    v_business_type,
    v_target_audience,
    v_value_proposition,
    jsonb_build_object(
      'forms_detected', p_html_content ILIKE '%<form%',
      'cta_buttons', (SELECT count(*) FROM regexp_split_to_table(p_html_content, '<button') WHERE length(trim(regexp_split_to_table)) > 0) - 1,
      'trust_signals', p_html_content ILIKE '%testimonial%' OR p_html_content ILIKE '%review%'
    ),
    jsonb_build_object(
      'word_count', array_length(string_to_array(trim(p_html_content), ' '), 1),
      'has_pricing', p_html_content ILIKE '%price%' OR p_html_content ILIKE '%$%',
      'contact_info', p_html_content ILIKE '%contact%' OR p_html_content ILIKE '%phone%' OR p_html_content ILIKE '%email%'
    )
  )
  ON CONFLICT (analysis_id) DO UPDATE SET
    html_content = EXCLUDED.html_content,
    headings = EXCLUDED.headings,
    images = EXCLUDED.images,
    links = EXCLUDED.links,
    business_type = EXCLUDED.business_type,
    target_audience_detected = EXCLUDED.target_audience_detected,
    value_proposition = EXCLUDED.value_proposition,
    conversion_elements = EXCLUDED.conversion_elements,
    content_analysis = EXCLUDED.content_analysis,
    extracted_at = now();
  
  -- Update analyses table with extracted context
  UPDATE analyses SET
    website_business_type = v_business_type,
    ai_target_audience = v_target_audience,
    ai_value_proposition = v_value_proposition,
    context_extracted = true
  WHERE id = p_analysis_id;
  
END;
$$ LANGUAGE plpgsql;

-- Create function to calculate lead generation score
CREATE OR REPLACE FUNCTION calculate_lead_generation_score(p_analysis_id uuid)
RETURNS numeric AS $$
DECLARE
  v_conversion_score numeric;
  v_performance_score numeric;
  v_seo_score numeric;
  v_trust_signals_count integer;
  v_forms_detected boolean;
  v_lead_score numeric;
BEGIN
  -- Get base scores
  SELECT score, performance_score, seo_score
  INTO v_conversion_score, v_performance_score, v_seo_score
  FROM analyses WHERE id = p_analysis_id;
  
  -- Get context factors
  SELECT 
    (conversion_elements->>'trust_signals')::boolean,
    (conversion_elements->>'forms_detected')::boolean
  INTO v_trust_signals_count, v_forms_detected
  FROM website_context WHERE analysis_id = p_analysis_id;
  
  -- Calculate weighted lead generation score (0-10 scale)
  v_lead_score := (
    COALESCE(v_conversion_score, 0) * 0.4 +
    COALESCE(v_performance_score, 0) / 10 * 0.3 +
    COALESCE(v_seo_score, 0) / 10 * 0.2 +
    (CASE WHEN v_trust_signals_count THEN 1 ELSE 0 END) * 0.05 +
    (CASE WHEN v_forms_detected THEN 1 ELSE 0 END) * 0.05
  );
  
  RETURN GREATEST(0, LEAST(10, v_lead_score));
END;
$$ LANGUAGE plpgsql;

-- Create trigger to extract context when lighthouse_data is updated
CREATE OR REPLACE FUNCTION trigger_extract_context()
RETURNS TRIGGER AS $$
BEGIN
  -- Extract context if lighthouse_data contains scraped data
  IF NEW.lighthouse_data IS NOT NULL AND 
     NEW.lighthouse_data ? 'scrapedData' AND
     NOT NEW.context_extracted THEN
    
    PERFORM extract_website_context(
      NEW.id,
      COALESCE(NEW.lighthouse_data->'scrapedData'->>'content', ''),
      NEW.lighthouse_data->'scrapedData'
    );
    
    -- Update lead generation score
    NEW.lead_generation_score := calculate_lead_generation_score(NEW.id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS trigger_extract_context ON analyses;
CREATE TRIGGER trigger_extract_context
  BEFORE UPDATE OF lighthouse_data ON analyses
  FOR EACH ROW
  EXECUTE FUNCTION trigger_extract_context();

-- Create views for easy data access
CREATE OR REPLACE VIEW analysis_insights_view AS
SELECT 
  a.id as analysis_id,
  a.url,
  a.title,
  a.score,
  a.lead_generation_score,
  a.website_business_type,
  a.ai_target_audience,
  a.ai_value_proposition,
  wc.business_type as detected_business_type,
  wc.target_audience_detected,
  wc.value_proposition as extracted_value_proposition,
  wc.conversion_elements,
  wc.content_analysis,
  -- Aggregate AI insights by type
  (
    SELECT jsonb_agg(
      jsonb_build_object(
        'id', ai.id,
        'title', ai.title,
        'description', ai.description,
        'evidence', ai.evidence,
        'impact_explanation', ai.impact_explanation,
        'implementation_steps', ai.implementation_steps,
        'business_value', ai.business_value,
        'priority_score', ai.priority_score,
        'category', ic.name
      )
    )
    FROM ai_insights ai
    JOIN insight_categories ic ON ai.category_id = ic.id
    WHERE ai.analysis_id = a.id AND ai.insight_type = 'strength'
  ) as ai_strengths,
  (
    SELECT jsonb_agg(
      jsonb_build_object(
        'id', ai.id,
        'title', ai.title,
        'description', ai.description,
        'evidence', ai.evidence,
        'impact_explanation', ai.impact_explanation,
        'implementation_steps', ai.implementation_steps,
        'business_value', ai.business_value,
        'priority_score', ai.priority_score,
        'category', ic.name
      )
    )
    FROM ai_insights ai
    JOIN insight_categories ic ON ai.category_id = ic.id
    WHERE ai.analysis_id = a.id AND ai.insight_type = 'weakness'
  ) as ai_weaknesses,
  (
    SELECT jsonb_agg(
      jsonb_build_object(
        'question', lqi.question_text,
        'context', lqi.context_explanation,
        'qualification', lqi.qualification_value,
        'response', lqi.suggested_response,
        'type', lqi.question_type,
        'priority', lqi.priority_level
      )
    )
    FROM lead_qualification_insights lqi
    WHERE lqi.analysis_id = a.id
  ) as lead_insights
FROM analyses a
LEFT JOIN website_context wc ON a.id = wc.analysis_id;

-- Grant permissions
GRANT EXECUTE ON FUNCTION extract_website_context(uuid, text, jsonb) TO service_role;
GRANT EXECUTE ON FUNCTION calculate_lead_generation_score(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION trigger_extract_context() TO service_role;
GRANT SELECT ON analysis_insights_view TO authenticated;

-- Create function to generate AI insights (to be called from API)
CREATE OR REPLACE FUNCTION generate_ai_insights_placeholder(p_analysis_id uuid)
RETURNS boolean AS $$
BEGIN
  -- This function will be called from the API after AI analysis
  -- It serves as a placeholder for the AI insight generation process
  UPDATE analyses SET ai_insights_generated = true WHERE id = p_analysis_id;
  RETURN true;
END;
$$ LANGUAGE plpgsql;

GRANT EXECUTE ON FUNCTION generate_ai_insights_placeholder(uuid) TO service_role;

-- Add helpful comments
COMMENT ON TABLE ai_insights IS 'Stores AI-generated contextual insights based on actual website analysis';
COMMENT ON TABLE website_context IS 'Extracted website data used for AI analysis context';
COMMENT ON TABLE lead_qualification_insights IS 'AI-generated lead qualification questions and concerns';
COMMENT ON TABLE performance_impact_insights IS 'AI-generated performance impact analysis with specific recommendations';
COMMENT ON VIEW analysis_insights_view IS 'Comprehensive view combining analysis data with AI-generated insights';