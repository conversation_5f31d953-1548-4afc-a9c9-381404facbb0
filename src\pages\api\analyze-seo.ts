// API endpoint for running SEO analysis
import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { runSEOAnalysis } from '../../lib/lighthouse-analyzer';
import { log } from '../../lib/logger';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const { url, analysisId } = await request.json();

    log.info(`Received SEO analysis request for URL: ${url} with analysisId: ${analysisId}`);

    if (!url || !analysisId) {
      log.warn('Missing URL or analysisId in SEO analysis request');
      return new Response(JSON.stringify({ error: 'Missing required parameters' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Normalize URL
    const normalizedUrl = url.startsWith('http') ? url : `https://${url}`;
    log.debug(`Normalized URL: ${normalizedUrl}`);

    // Run SEO analysis
    const seoAnalysis = await runSEOAnalysis(normalizedUrl);

    // Store in database
    log.debug('Storing SEO results in database...');
    const { error: updateError } = await supabaseAdmin
      .from('analyses')
      .update({
        seo_score: seoAnalysis.score,
        seo_data: {
          ...seoAnalysis.data,
          pros: seoAnalysis.pros,
          cons: seoAnalysis.cons
        }
      })
      .eq('id', analysisId);

    if (updateError) {
      log.error(`Database update error: ${updateError.message}`);
    } else {
      log.info('SEO results stored in database.');
    }

    // Store SEO issues separately
    if (seoAnalysis.issues.length > 0) {
      const { error: issuesError } = await supabaseAdmin
        .from('seo_issues')
        .insert(
          seoAnalysis.issues.map(issue => ({
            analysis_id: analysisId,
            issue: issue.issue,
            recommendation: issue.recommendation,
            severity_score: issue.severity_score
          }))
        );

      if (issuesError) {
        log.error(`Error storing SEO issues: ${issuesError.message}`);
      } else {
        log.info('SEO issues stored in database.');
      }
    }

    return new Response(JSON.stringify({
      success: true,
      score: seoAnalysis.score,
      issues: seoAnalysis.issues,
      data: seoAnalysis.data,
      pros: seoAnalysis.pros,
      cons: seoAnalysis.cons
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`SEO analysis error: ${error instanceof Error ? error.message : error}`);

    return new Response(JSON.stringify({
      error: 'SEO analysis failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};